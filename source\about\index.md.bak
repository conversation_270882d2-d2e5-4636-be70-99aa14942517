---
title: 关于本人
date: 2025-08-14 14:46:08
comments: true
description: 了解更多关于我的信息
top_img: false
aside: false
---

<link rel="stylesheet" href="/css/about.css">

<!-- 关于页面主容器 -->
<div class="about-page-container">

  <!-- 顶部Banner区域 -->
  <div class="about-banner-wrapper">
    <div class="about-banner-bg"></div>
    <div class="about-banner-content">
      <div class="banner-avatar">
        <img src="https://i.postimg.cc/Pq3nWf2b/132669446-p0.jpg" alt="郁离">
        <div class="avatar-status">🎯</div>
      </div>
      <div class="banner-info">
        <h1 class="banner-title">Hi, I'm <span class="gradient-text">郁离</span></h1>
        <p class="banner-subtitle">一个热爱技术与生活的前端开发者</p>
        <div class="banner-tags">
          <span class="banner-tag">🚀 前端工程师</span>
          <span class="banner-tag">✨ 开源爱好者</span>
          <span class="banner-tag">📝 技术博主</span>
        </div>
        <div class="banner-social">
          <a href="https://github.com/your-username" class="social-icon" title="GitHub">
            <i class="fab fa-github"></i>
          </a>
          <a href="mailto:<EMAIL>" class="social-icon" title="Email">
            <i class="fas fa-envelope"></i>
          </a>
          <a href="#" class="social-icon" title="微信">
            <i class="fab fa-weixin"></i>
          </a>
          <a href="#" class="social-icon" title="QQ">
            <i class="fab fa-qq"></i>
          </a>
        </div>
      </div>
    </div>
    <div class="about-scroll-down">
      <i class="fas fa-angle-down"></i>
    </div>
  </div>

  <!-- 统计数据区域 -->
  <div class="about-stats-section">
    <div class="stats-container">
      <div class="stat-item">
        <div class="stat-value" data-value="30">0</div>
        <div class="stat-label">文章数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" data-value="15">0</div>
        <div class="stat-label">分类数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" data-value="50">0</div>
        <div class="stat-label">标签数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" data-value="2">0</div>
        <div class="stat-label">运行年</div>
      </div>
    </div>
  </div>

  <!-- 个人介绍区域 -->
  <div class="about-intro-section">
    <div class="section-header">
      <h2 class="section-title">🎯 关于我</h2>
      <div class="section-line"></div>
    </div>
    <div class="intro-content">
      <p>你好！我是郁离，一名充满热情的前端开发工程师。</p>
      <p>我热爱编程，享受将创意转化为现实的过程。在这里，我分享技术见解、开发经验和生活感悟。</p>
      <p>除了编码，我还喜欢摄影、音乐和阅读。相信技术与艺术的结合能创造更美好的体验。</p>
    </div>
  </div>

  <!-- 技能树区域 -->
  <div class="about-skills-section">
    <div class="section-header">
      <h2 class="section-title">💻 技能树</h2>
      <div class="section-line"></div>
    </div>
    <div class="skills-tabs">
      <div class="skill-tab active" data-tab="frontend">前端技术</div>
      <div class="skill-tab" data-tab="backend">后端技术</div>
      <div class="skill-tab" data-tab="tools">开发工具</div>
      <div class="skill-tab" data-tab="others">其他技能</div>
    </div>
    <div class="skills-content">
      <div class="skill-panel active" id="frontend">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">🎨</div>
            <div class="skill-name">HTML5</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 95%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🎭</div>
            <div class="skill-name">CSS3</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 90%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">⚡</div>
            <div class="skill-name">JavaScript</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 88%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">📘</div>
            <div class="skill-name">TypeScript</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 80%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">⚛️</div>
            <div class="skill-name">React</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 85%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">💚</div>
            <div class="skill-name">Vue.js</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 82%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="backend">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">🟢</div>
            <div class="skill-name">Node.js</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 75%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🚂</div>
            <div class="skill-name">Express</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 70%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🐬</div>
            <div class="skill-name">MySQL</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 65%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🍃</div>
            <div class="skill-name">MongoDB</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="tools">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">💻</div>
            <div class="skill-name">VS Code</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 95%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🔧</div>
            <div class="skill-name">Git</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 88%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">📦</div>
            <div class="skill-name">Webpack</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 75%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">🐳</div>
            <div class="skill-name">Docker</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="others">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">🎨</div>
            <div class="skill-name">Figma</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 70%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">📸</div>
            <div class="skill-name">Photoshop</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 65%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 项目经历时间线 -->
  <div class="about-timeline-section">
    <div class="section-header">
      <h2 class="section-title">📅 我的历程</h2>
      <div class="section-line"></div>
    </div>
    <div class="timeline-wrapper">
      <div class="timeline-line"></div>
      <div class="timeline-item left">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2023 - 至今</div>
          <div class="timeline-title">前端开发工程师</div>
          <div class="timeline-desc">负责公司核心产品的前端开发，优化用户体验，提升页面性能</div>
        </div>
      </div>
      <div class="timeline-item right">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2022</div>
          <div class="timeline-title">个人博客建立</div>
          <div class="timeline-desc">基于Hexo和AnZhiYu主题搭建个人技术博客，分享学习心得</div>
        </div>
      </div>
      <div class="timeline-item left">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2021</div>
          <div class="timeline-title">开始前端之旅</div>
          <div class="timeline-desc">系统学习前端技术栈，参与开源项目，不断提升技术能力</div>
        </div>
      </div>
      <div class="timeline-item right">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2020</div>
          <div class="timeline-title">大学毕业</div>
          <div class="timeline-desc">计算机科学与技术专业，获得学士学位</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 兴趣爱好区域 -->
  <div class="about-hobbies-section">
    <div class="section-header">
      <h2 class="section-title">🎯 兴趣爱好</h2>
      <div class="section-line"></div>
    </div>
    <div class="hobbies-grid">
      <div class="hobby-card">
        <div class="hobby-icon">💻</div>
        <div class="hobby-title">编程</div>
        <div class="hobby-desc">热爱编码，享受创造的过程</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">📸</div>
        <div class="hobby-title">摄影</div>
        <div class="hobby-desc">用镜头记录生活的美好瞬间</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">🎵</div>
        <div class="hobby-title">音乐</div>
        <div class="hobby-desc">吉他弹唱，用音乐表达情感</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">📚</div>
        <div class="hobby-title">阅读</div>
        <div class="hobby-desc">技术书籍与人文社科并重</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">🏃</div>
        <div class="hobby-title">运动</div>
        <div class="hobby-desc">跑步健身，保持健康活力</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">✈️</div>
        <div class="hobby-title">旅行</div>
        <div class="hobby-desc">探索世界，体验不同文化</div>
      </div>
    </div>
  </div>

  <!-- 联系方式区域 -->
  <div class="about-contact-section">
    <div class="section-header">
      <h2 class="section-title">📮 联系我</h2>
      <div class="section-line"></div>
    </div>
    <div class="contact-content">
      <p class="contact-intro">如果你有任何问题或建议，欢迎通过以下方式联系我：</p>
      <div class="contact-grid">
        <a href="mailto:<EMAIL>" class="contact-card">
          <div class="contact-icon">📧</div>
          <div class="contact-title">邮箱</div>
          <div class="contact-info"><EMAIL></div>
        </a>
        <a href="https://github.com/your-username" class="contact-card">
          <div class="contact-icon">🐙</div>
          <div class="contact-title">GitHub</div>
          <div class="contact-info">@your-username</div>
        </a>
        <a href="#" class="contact-card">
          <div class="contact-icon">💬</div>
          <div class="contact-title">微信</div>
          <div class="contact-info">your-wechat</div>
        </a>
        <a href="#" class="contact-card">
          <div class="contact-icon">🐧</div>
          <div class="contact-title">QQ</div>
          <div class="contact-info">123456789</div>
        </a>
      </div>
    </div>
  </div>

  <!-- 座右铭区域 -->
  <div class="about-motto-section">
    <div class="motto-card">
      <div class="motto-icon">💭</div>
      <div class="motto-text">"Stay hungry, stay foolish."</div>
      <div class="motto-author">—— Steve Jobs</div>
    </div>
  </div>

</div>

<script>
// 技能标签切换
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('.skill-tab');
  const panels = document.querySelectorAll('.skill-panel');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetTab = this.getAttribute('data-tab');
      
      tabs.forEach(t => t.classList.remove('active'));
      panels.forEach(p => p.classList.remove('active'));
      
      this.classList.add('active');
      document.getElementById(targetTab).classList.add('active');
    });
  });
  
  // 数字动画
  const observerOptions = {
    threshold: 0.5
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const statValues = entry.target.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
          const finalValue = parseInt(stat.getAttribute('data-value'));
          let currentValue = 0;
          const increment = finalValue / 50;
          const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
              currentValue = finalValue;
              clearInterval(timer);
            }
            stat.textContent = Math.floor(currentValue);
          }, 30);
        });
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);
  
  const statsSection = document.querySelector('.about-stats-section');
  if (statsSection) {
    observer.observe(statsSection);
  }
  
  // 滚动箭头
  const scrollDown = document.querySelector('.about-scroll-down');
  if (scrollDown) {
    scrollDown.addEventListener('click', () => {
      window.scrollTo({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    });
  }
});
</script>

<div class="about-page">
  <!-- 头部个人信息卡片 -->
  <div class="about-header-card">
    <div class="about-bg-decoration">
      <div class="bg-circle circle-1"></div>
      <div class="bg-circle circle-2"></div>
      <div class="bg-circle circle-3"></div>
    </div>
    <div class="about-avatar-section">
      <div class="avatar-container">
        <img src="https://i.postimg.cc/Pq3nWf2b/132669446-p0.jpg" alt="郁离" class="about-avatar-img">
        <div class="avatar-ring"></div>
      </div>
      <div class="about-info">
        <h1 class="about-name">
          <span class="name-text">郁离</span>
          <span class="name-subtitle">YuLi</span>
        </h1>
        <div class="about-description">
          <span class="desc-icon">✨</span>
          一个热爱技术、喜欢分享的前端开发者
        </div>
        <div class="about-tags">
          <span class="tag tag-blue">前端工程师</span>
          <span class="tag tag-green">技术博主</span>
          <span class="tag tag-purple">开源爱好者</span>
        </div>
        <div class="about-social">
          <a href="mailto:<EMAIL>" class="social-link social-email" title="邮箱">
            <i class="anzhiyufont anzhiyu-icon-envelope"></i>
          </a>
          <a href="https://github.com/your-username" class="social-link social-github" title="GitHub">
            <i class="anzhiyufont anzhiyu-icon-github"></i>
          </a>
          <a href="#" class="social-link social-qq" title="QQ">
            <i class="anzhiyufont anzhiyu-icon-qq"></i>
          </a>
          <a href="#" class="social-link social-wechat" title="微信">
            <i class="anzhiyufont anzhiyu-icon-wechat"></i>
          </a>
        </div>
      </div>
    </div>
  </div>

<div class="about-content">

## 📝 个人简介

我是一名前端开发工程师，热爱技术，喜欢分享。在这里记录学习与生活，分享技术心得与人生感悟。

### 基本信息

<div class="info-grid">
  <div class="info-item">
    <div class="info-icon">🏷️</div>
    <div class="info-content">
      <div class="info-label">昵称</div>
      <div class="info-value">郁离</div>
    </div>
  </div>
  <div class="info-item">
    <div class="info-icon">📍</div>
    <div class="info-content">
      <div class="info-label">位置</div>
      <div class="info-value">中国</div>
    </div>
  </div>
  <div class="info-item">
    <div class="info-icon">💼</div>
    <div class="info-content">
      <div class="info-label">职业</div>
      <div class="info-value">前端开发工程师</div>
    </div>
  </div>
  <div class="info-item">
    <div class="info-icon">🎓</div>
    <div class="info-content">
      <div class="info-label">学历</div>
      <div class="info-value">本科</div>
    </div>
  </div>
</div>

## 💻 技能栈

<div class="skills-container">
  <div class="skill-category">
    <h3>前端技术</h3>
    <div class="skill-tags">
      <span class="skill-tag">HTML5</span>
      <span class="skill-tag">CSS3</span>
      <span class="skill-tag">JavaScript</span>
      <span class="skill-tag">TypeScript</span>
      <span class="skill-tag">React</span>
      <span class="skill-tag">Vue.js</span>
      <span class="skill-tag">Next.js</span>
      <span class="skill-tag">Nuxt.js</span>
    </div>
  </div>

  <div class="skill-category">
    <h3>开发工具</h3>
    <div class="skill-tags">
      <span class="skill-tag">VS Code</span>
      <span class="skill-tag">Git</span>
      <span class="skill-tag">Webpack</span>
      <span class="skill-tag">Vite</span>
      <span class="skill-tag">npm</span>
      <span class="skill-tag">yarn</span>
      <span class="skill-tag">Docker</span>
      <span class="skill-tag">Figma</span>
    </div>
  </div>

  <div class="skill-category">
    <h3>后端技术</h3>
    <div class="skill-tags">
      <span class="skill-tag">Node.js</span>
      <span class="skill-tag">Express</span>
      <span class="skill-tag">MySQL</span>
      <span class="skill-tag">MongoDB</span>
      <span class="skill-tag">Redis</span>
      <span class="skill-tag">Nginx</span>
    </div>
  </div>

  <div class="skill-category">
    <h3>云服务</h3>
    <div class="skill-tags">
      <span class="skill-tag">Vercel</span>
      <span class="skill-tag">阿里云</span>
      <span class="skill-tag">腾讯云</span>
      <span class="skill-tag">GitHub Pages</span>
    </div>
  </div>
</div>

## 🎯 兴趣爱好

<div class="hobby-grid">
  <div class="hobby-item">
    <div class="hobby-icon">💻</div>
    <div class="hobby-title">技术学习</div>
    <div class="hobby-desc">持续关注前端新技术，参与开源项目</div>
  </div>
  <div class="hobby-item">
    <div class="hobby-icon">📸</div>
    <div class="hobby-title">摄影</div>
    <div class="hobby-desc">记录生活中的美好瞬间</div>
  </div>
  <div class="hobby-item">
    <div class="hobby-icon">🎵</div>
    <div class="hobby-title">音乐</div>
    <div class="hobby-desc">吉他、钢琴爱好者，喜欢各种音乐风格</div>
  </div>
  <div class="hobby-item">
    <div class="hobby-icon">📚</div>
    <div class="hobby-title">阅读</div>
    <div class="hobby-desc">技术书籍、人文社科、在线课程</div>
  </div>
  <div class="hobby-item">
    <div class="hobby-icon">🏃</div>
    <div class="hobby-title">运动</div>
    <div class="hobby-desc">跑步、羽毛球，保持健康生活</div>
  </div>
  <div class="hobby-item">
    <div class="hobby-icon">✍️</div>
    <div class="hobby-title">写作</div>
    <div class="hobby-desc">技术博客分享，记录学习心得</div>
  </div>
</div>

## 📈 项目经历

<div class="timeline">
  <div class="timeline-item">
    <div class="timeline-date">2022 - 至今</div>
    <div class="timeline-content">
      <h4>前端开发工程师</h4>
      <p>负责公司官网和管理系统开发，参与移动端项目，优化性能提升用户体验</p>
    </div>
  </div>
  <div class="timeline-item">
    <div class="timeline-date">2021</div>
    <div class="timeline-content">
      <h4>个人博客项目</h4>
      <p>基于 Hexo + AnZhiYu 主题开发个人博客，分享技术文章和生活感悟</p>
    </div>
  </div>
  <div class="timeline-item">
    <div class="timeline-date">2020</div>
    <div class="timeline-content">
      <h4>获得软件设计师证书</h4>
      <p>通过软件设计师考试，提升专业技能水平</p>
    </div>
  </div>
</div>

## 📞 联系我

如果你想要交流技术问题或者有任何建议，欢迎通过以下方式联系我：

<div class="contact-links">
  <a href="mailto:<EMAIL>" class="contact-link-item">
    <i class="anzhiyufont anzhiyu-icon-envelope"></i>
    <span>邮箱联系</span>
  </a>
  <a href="https://github.com/your-username" class="contact-link-item">
    <i class="anzhiyufont anzhiyu-icon-github"></i>
    <span>GitHub</span>
  </a>
  <a href="#" class="contact-link-item">
    <i class="anzhiyufont anzhiyu-icon-qq"></i>
    <span>QQ交流</span>
  </a>
  <a href="#" class="contact-link-item">
    <i class="anzhiyufont anzhiyu-icon-wechat"></i>
    <span>微信</span>
  </a>
</div>

## 💭 座右铭

{% note info modern %}
> "代码如诗，生活如歌。用技术改变世界，用心感受生活。"
{% endnote %}

---

感谢你的访问，期待与你的交流！🎉

</div>

<style>
/* 多彩关于页面样式 - 仿照官方演示站点 */
.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* 头部卡片 */
.about-header-card {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.about-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 15%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.about-avatar-section {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.avatar-container {
  position: relative;
}

.about-avatar-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.about-avatar-img:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.avatar-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.1); opacity: 0; }
}

.about-info {
  flex: 1;
  color: white;
}

.about-name {
  margin: 0 0 1rem 0;
}

.name-text {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
}

.name-subtitle {
  display: block;
  font-size: 1.2rem;
  opacity: 0.8;
  font-weight: 300;
}

.about-description {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.desc-icon {
  font-size: 1.4rem;
}

.about-tags {
  display: flex;
  gap: 0.8rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.tag {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tag-blue {
  background: rgba(74, 144, 226, 0.3);
  color: #e3f2fd;
}

.tag-green {
  background: rgba(76, 175, 80, 0.3);
  color: #e8f5e8;
}

.tag-purple {
  background: rgba(156, 39, 176, 0.3);
  color: #f3e5f5;
}

.tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.about-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.3rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-email {
  background: rgba(244, 67, 54, 0.3);
  color: #ffebee;
}

.social-github {
  background: rgba(33, 33, 33, 0.3);
  color: #f5f5f5;
}

.social-qq {
  background: rgba(0, 188, 212, 0.3);
  color: #e0f2f1;
}

.social-wechat {
  background: rgba(76, 175, 80, 0.3);
  color: #e8f5e8;
}

.social-link:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.about-content {
  max-width: 100%;
}

.about-content h2 {
  color: var(--anzhiyu-main);
  font-size: 1.8rem;
  margin: 3rem 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(var(--anzhiyu-main-rgb), 0.2);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  border: 1px solid var(--anzhiyu-card-border);
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--anzhiyu-shadow-lightblack);
}

.info-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 0.9rem;
  color: var(--anzhiyu-secondtext);
  margin-bottom: 0.3rem;
}

.info-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
}

/* 技能容器 */
.skills-container {
  display: grid;
  gap: 2rem;
  margin: 2rem 0;
}

.skill-category {
  background: var(--anzhiyu-card-bg);
  padding: 2.5rem;
  border-radius: 16px;
  border: 1px solid var(--anzhiyu-card-border);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.skill-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
  background-size: 300% 100%;
  animation: rainbow 4s ease infinite;
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.skill-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.skill-category h3 {
  color: var(--anzhiyu-main);
  margin: 0 0 1.8rem 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.skill-tag {
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

/* 不同颜色的技能标签 */
.skill-tag:nth-child(6n+1) {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.skill-tag:nth-child(6n+2) {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}

.skill-tag:nth-child(6n+3) {
  background: linear-gradient(135deg, #45b7d1, #2196f3);
  color: white;
}

.skill-tag:nth-child(6n+4) {
  background: linear-gradient(135deg, #96ceb4, #4caf50);
  color: white;
}

.skill-tag:nth-child(6n+5) {
  background: linear-gradient(135deg, #feca57, #ff9f43);
  color: white;
}

.skill-tag:nth-child(6n+6) {
  background: linear-gradient(135deg, #ff9ff3, #e91e63);
  color: white;
}

.skill-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 兴趣爱好网格 */
.hobby-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.hobby-item {
  text-align: center;
  padding: 2.5rem;
  background: var(--anzhiyu-card-bg);
  border-radius: 20px;
  border: 1px solid var(--anzhiyu-card-border);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.hobby-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: all 0.6s ease;
}

.hobby-item:hover::before {
  left: 100%;
}

.hobby-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 不同颜色的兴趣卡片 */
.hobby-item:nth-child(6n+1) {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 82, 0.05));
  border-color: rgba(255, 107, 107, 0.2);
}

.hobby-item:nth-child(6n+2) {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(68, 160, 141, 0.05));
  border-color: rgba(78, 205, 196, 0.2);
}

.hobby-item:nth-child(6n+3) {
  background: linear-gradient(135deg, rgba(69, 183, 209, 0.1), rgba(33, 150, 243, 0.05));
  border-color: rgba(69, 183, 209, 0.2);
}

.hobby-item:nth-child(6n+4) {
  background: linear-gradient(135deg, rgba(150, 206, 180, 0.1), rgba(76, 175, 80, 0.05));
  border-color: rgba(150, 206, 180, 0.2);
}

.hobby-item:nth-child(6n+5) {
  background: linear-gradient(135deg, rgba(254, 202, 87, 0.1), rgba(255, 159, 67, 0.05));
  border-color: rgba(254, 202, 87, 0.2);
}

.hobby-item:nth-child(6n+6) {
  background: linear-gradient(135deg, rgba(255, 159, 243, 0.1), rgba(233, 30, 99, 0.05));
  border-color: rgba(255, 159, 243, 0.2);
}

.hobby-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: all 0.3s ease;
}

.hobby-item:hover .hobby-icon {
  transform: scale(1.1) rotate(5deg);
}

.hobby-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--anzhiyu-main);
  margin-bottom: 1rem;
}

.hobby-desc {
  color: var(--anzhiyu-secondtext);
  line-height: 1.7;
  font-size: 0.95rem;
}

.timeline {
  position: relative;
  margin: 2rem 0;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--anzhiyu-main);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 0;
  width: 12px;
  height: 12px;
  background: var(--anzhiyu-main);
  border-radius: 50%;
  border: 3px solid var(--anzhiyu-bg);
}

.timeline-date {
  font-size: 0.9rem;
  color: var(--anzhiyu-main);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-content h4 {
  color: var(--anzhiyu-fontcolor);
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.timeline-content p {
  color: var(--anzhiyu-secondtext);
  margin: 0;
  line-height: 1.6;
}

.contact-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.contact-link-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 10px;
  text-decoration: none;
  color: var(--anzhiyu-fontcolor);
  transition: all 0.3s ease;
}

.contact-link-item:hover {
  background: var(--anzhiyu-main);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(var(--anzhiyu-main-rgb), 0.3);
}

.contact-link-item i {
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .about-avatar {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .about-name {
    font-size: 2rem;
  }

  .about-description {
    font-size: 1rem;
  }

  .info-grid, .hobby-grid {
    grid-template-columns: 1fr;
  }

  .skill-category {
    padding: 1.5rem;
  }

  .timeline {
    padding-left: 1.5rem;
  }

  .timeline-item {
    padding-left: 1.5rem;
  }

  .contact-links {
    grid-template-columns: 1fr;
  }
}

/* 深色模式适配 */
[data-theme="dark"] .about-avatar {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-card-border);
}

[data-theme="dark"] .info-item,
[data-theme="dark"] .skill-category,
[data-theme="dark"] .hobby-item,
[data-theme="dark"] .contact-link-item {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-card-border);
}
</style>

<style>
.about-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 0;
  background: linear-gradient(135deg, rgba(var(--anzhiyu-main-rgb), 0.1), rgba(var(--anzhiyu-main-rgb), 0.05));
  border-radius: 15px;
  position: relative;
}

.about-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  border-radius: 15px;
  pointer-events: none;
}

.about-intro {
  font-size: 1.2rem;
  color: var(--anzhiyu-secondtext);
  margin: 1.5rem 0;
  position: relative;
  z-index: 1;
}

.about-content {
  max-width: 100%;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.skill-item {
  background: var(--anzhiyu-card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid var(--anzhiyu-card-border);
  transition: all 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--anzhiyu-shadow-lightblack);
}

.skill-icon {
  font-size: 2rem;
  display: block;
  margin-bottom: 0.5rem;
}

.skill-item h4 {
  margin: 0.5rem 0;
  color: var(--anzhiyu-fontcolor);
}

.skill-bar {
  width: 100%;
  height: 8px;
  background: var(--anzhiyu-bg);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.8rem;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  border-radius: 4px;
  transition: width 1s ease;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.contact-item {
  background: var(--anzhiyu-card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid var(--anzhiyu-card-border);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.contact-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--anzhiyu-shadow-border);
  border-color: var(--anzhiyu-main);
}

.contact-icon {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 0.8rem;
}

.contact-item h4 {
  margin: 0.5rem 0;
  color: var(--anzhiyu-main);
  font-size: 1.1rem;
}

.contact-item p {
  margin: 0;
  color: var(--anzhiyu-secondtext);
  font-size: 0.9rem;
}

.about-content h2 {
  color: var(--anzhiyu-main);
  border-left: 4px solid var(--anzhiyu-main);
  padding-left: 1rem;
  margin: 3rem 0 1.5rem 0;
}

.about-content h3 {
  color: var(--anzhiyu-fontcolor);
  margin: 2rem 0 1rem 0;
  font-size: 1.3rem;
}

.about-content ul {
  list-style: none;
  padding: 0;
}

.about-content li {
  padding: 0.5rem 0;
  border-bottom: 1px dashed var(--anzhiyu-card-border);
  transition: all 0.3s ease;
}

.about-content li:hover {
  color: var(--anzhiyu-main);
  padding-left: 0.5rem;
}

.about-content li:last-child {
  border-bottom: none;
}

@media (max-width: 768px) {
  .about-header {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .about-intro {
    font-size: 1rem;
  }

  .skills-grid, .contact-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .skill-item, .contact-item {
    padding: 1rem;
  }
}
</style>
