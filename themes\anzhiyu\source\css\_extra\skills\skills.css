#skills-tags-group-all {
  display: flex;
  transform: rotate(0);
  transition: 0.3s;
  overflow: visible !important;
  width: 100%;
  position: relative;
  margin-top: 30px !important;
  margin-bottom: 20px !important;
}
#skills-tags-group-all .tags-group-wrapper {
  margin-top: 90px !important;
  display: flex;
  flex-wrap: nowrap;
  animation: rowup 30s linear infinite;
  width: max-content;
}
#skills-tags-group-all .tags-group-icon-pair {
  margin-left: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

#skills-tags-group-all .tags-group-icon {
  width: 80px !important;
  height: 80px !important;
  min-width: 80px !important;
  min-height: 80px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  position: relative !important;
}

#skills-tags-group-all .tags-group-icon img {
  width: 48px !important;
  height: 48px !important;
  min-width: 48px !important;
  min-height: 48px !important;
  max-width: 48px !important;
  max-height: 48px !important;
  object-fit: contain !important;
  object-position: center !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

#skills-tags-group-all .tags-group-icon:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}


#skills-tags-group-all .etc {
  margin-right: 10px;
  margin-top: 10px;
}

/* 确保父容器有足够高度 */
.author-content-item.skills {
  min-height: 350px !important;
  overflow: visible !important;
}

.skills-style-group {
  overflow: visible !important;
  height: auto !important;
  min-height: 300px !important;
}

@keyframes rowup {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}
