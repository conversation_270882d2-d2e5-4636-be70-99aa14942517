<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论图片上传测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #FF6B6B;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
            margin: 20px 0;
        }
        
        .upload-area:hover {
            border-color: #FF6B6B;
            background: #fff5f5;
        }
        
        .upload-area.dragover {
            border-color: #FF6B6B;
            background: #fff5f5;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .upload-text {
            color: #666;
            font-size: 16px;
        }
        
        .upload-hint {
            color: #999;
            font-size: 12px;
            margin-top: 10px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: #FF6B6B;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #FF5252;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #4CAF50;
        }
        
        .btn-success:hover {
            background: #45a049;
        }
        
        .status {
            padding: 12px 16px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: 500;
            border-left: 4px solid;
        }
        
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }
        
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            margin: 10px;
        }
        
        .image-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
            width: 0%;
            transition: width 0.3s;
        }
        
        .uploaded-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .uploaded-item {
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .uploaded-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .uploaded-item .name {
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }
        
        .uploaded-item .size {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }
        
        .markdown-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .copy-btn {
            background: #6c757d;
            font-size: 12px;
            padding: 5px 10px;
        }
        
        .copy-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 评论图片上传测试</h1>
            <p>测试评论系统的图片上传功能</p>
        </div>
        
        <div class="content">
            <!-- 图片上传区域 -->
            <div class="test-section">
                <h3>📤 图片上传</h3>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击或拖拽图片到这里上传</div>
                    <div class="upload-hint">支持 JPG、PNG、GIF、WebP 格式，最大 5MB</div>
                </div>
                
                <input type="file" id="fileInput" class="file-input" accept="image/*" multiple>
                
                <div style="text-align: center; margin: 20px 0;">
                    <button class="btn" onclick="selectFile()">📁 选择文件</button>
                    <button class="btn btn-success" onclick="uploadSelected()" id="uploadBtn" disabled>🚀 上传图片</button>
                    <button class="btn" onclick="clearAll()">🗑️ 清空</button>
                </div>
                
                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div id="uploadResult"></div>
            </div>
            
            <!-- 图片预览 -->
            <div class="test-section">
                <h3>👀 图片预览</h3>
                <div id="imagePreview"></div>
            </div>
            
            <!-- 上传历史 -->
            <div class="test-section">
                <h3>📋 上传历史</h3>
                <button class="btn" onclick="loadUploadedImages()">🔄 刷新列表</button>
                <div id="uploadedImages" class="uploaded-images"></div>
            </div>
            
            <!-- Markdown代码 -->
            <div class="test-section">
                <h3>📝 Markdown 代码</h3>
                <p>上传成功后，可以复制下面的Markdown代码到评论中使用：</p>
                <div id="markdownOutput" class="markdown-output">暂无上传的图片</div>
                <button class="btn copy-btn" onclick="copyMarkdown()">📋 复制代码</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8360';
        let selectedFiles = [];
        let uploadedImages = [];
        
        // DOM 元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        
        // 绑定事件
        uploadArea.addEventListener('click', selectFile);
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
        fileInput.addEventListener('change', handleFileSelect);
        
        // 选择文件
        function selectFile() {
            fileInput.click();
        }
        
        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }
        
        // 处理拖拽
        function handleDragOver(event) {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        }
        
        function handleDragLeave(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
        }
        
        function handleDrop(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length !== files.length) {
                showResult('warning', '只能上传图片文件，其他文件已被忽略');
            }
            
            addFiles(imageFiles);
        }
        
        // 添加文件到选择列表
        function addFiles(files) {
            const validFiles = files.filter(file => {
                if (!file.type.startsWith('image/')) {
                    showResult('error', `${file.name} 不是有效的图片文件`);
                    return false;
                }
                if (file.size > 5 * 1024 * 1024) {
                    showResult('error', `${file.name} 文件太大，最大支持 5MB`);
                    return false;
                }
                return true;
            });
            
            selectedFiles = [...selectedFiles, ...validFiles];
            updateUploadButton();
            showImagePreview();
            
            if (validFiles.length > 0) {
                showResult('info', `已选择 ${validFiles.length} 个文件`);
            }
        }
        
        // 更新上传按钮状态
        function updateUploadButton() {
            uploadBtn.disabled = selectedFiles.length === 0;
            uploadBtn.textContent = selectedFiles.length > 0 
                ? `🚀 上传 ${selectedFiles.length} 个图片` 
                : '🚀 上传图片';
        }
        
        // 显示图片预览
        function showImagePreview() {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = '';
            
            if (selectedFiles.length === 0) {
                previewDiv.innerHTML = '<p style="text-align: center; color: #999;">暂无选择的图片</p>';
                return;
            }
            
            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.style.display = 'inline-block';
                    div.style.margin = '10px';
                    div.style.textAlign = 'center';
                    
                    div.innerHTML = `
                        <img src="${e.target.result}" class="preview-image" style="max-width: 200px; max-height: 150px;">
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            ${file.name}<br>
                            ${formatFileSize(file.size)}
                        </div>
                        <button class="btn" style="font-size: 12px; padding: 5px 10px; margin-top: 5px;" 
                                onclick="removeFile(${index})">❌ 移除</button>
                    `;
                    
                    previewDiv.appendChild(div);
                };
                reader.readAsDataURL(file);
            });
        }
        
        // 移除文件
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateUploadButton();
            showImagePreview();
            showResult('info', '文件已移除');
        }
        
        // 上传选中的文件
        async function uploadSelected() {
            if (selectedFiles.length === 0) return;
            
            showResult('info', '开始上传图片...');
            progressBar.style.display = 'block';
            uploadBtn.disabled = true;
            
            const results = [];
            
            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = (i / selectedFiles.length) * 100;
                
                progressFill.style.width = progress + '%';
                showResult('info', `正在上传 ${file.name} (${i + 1}/${selectedFiles.length})`);
                
                try {
                    const result = await uploadSingleFile(file);
                    results.push({ file, result, success: true });
                } catch (error) {
                    results.push({ file, error, success: false });
                }
            }
            
            progressFill.style.width = '100%';
            
            // 显示结果
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;
            
            if (successCount === results.length) {
                showResult('success', `✅ 所有图片上传成功！(${successCount}/${results.length})`);
            } else if (successCount > 0) {
                showResult('warning', `⚠️ 部分图片上传成功：${successCount} 成功，${failCount} 失败`);
            } else {
                showResult('error', `❌ 所有图片上传失败！`);
            }
            
            // 更新上传历史
            const successResults = results.filter(r => r.success);
            uploadedImages = [...uploadedImages, ...successResults.map(r => r.result.data)];
            updateMarkdownOutput();
            
            // 清理
            setTimeout(() => {
                progressBar.style.display = 'none';
                progressFill.style.width = '0%';
                uploadBtn.disabled = false;
                selectedFiles = [];
                updateUploadButton();
                showImagePreview();
            }, 2000);
        }
        
        // 上传单个文件
        function uploadSingleFile(file) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('image', file);
                
                const xhr = new XMLHttpRequest();
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.errno === 0) {
                            resolve(response);
                        } else {
                            reject(new Error(response.errmsg || '上传失败'));
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}`));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('网络错误'));
                };
                
                xhr.open('POST', `${API_BASE}/upload`);
                xhr.send(formData);
            });
        }
        
        // 加载已上传的图片
        async function loadUploadedImages() {
            try {
                showResult('info', '正在加载图片列表...');
                
                // 这里应该有一个获取已上传图片列表的API
                // 目前我们只显示本次会话中上传的图片
                const container = document.getElementById('uploadedImages');
                
                if (uploadedImages.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #999; grid-column: 1/-1;">暂无上传的图片</p>';
                    return;
                }
                
                container.innerHTML = uploadedImages.map(image => `
                    <div class="uploaded-item">
                        <img src="${API_BASE}${image.url}" alt="${image.filename}" 
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <div style="display: none; padding: 40px; color: #999;">图片加载失败</div>
                        <div class="name">${image.filename}</div>
                        <div class="size">${formatFileSize(image.size)} | ${image.type}</div>
                        <button class="btn copy-btn" onclick="copyImageUrl('${API_BASE}${image.url}')">📋 复制链接</button>
                    </div>
                `).join('');
                
                showResult('success', `已加载 ${uploadedImages.length} 张图片`);
                
            } catch (error) {
                showResult('error', `加载图片列表失败: ${error.message}`);
            }
        }
        
        // 更新Markdown输出
        function updateMarkdownOutput() {
            const output = document.getElementById('markdownOutput');
            
            if (uploadedImages.length === 0) {
                output.textContent = '暂无上传的图片';
                return;
            }
            
            const markdown = uploadedImages.map(image => 
                `![${image.filename}](${API_BASE}${image.url})`
            ).join('\n');
            
            output.textContent = markdown;
        }
        
        // 复制Markdown代码
        function copyMarkdown() {
            const output = document.getElementById('markdownOutput');
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(output.textContent).then(() => {
                    showResult('success', '📋 Markdown代码已复制到剪贴板');
                }).catch(() => {
                    showResult('error', '复制失败，请手动复制');
                });
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = output.textContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showResult('success', '📋 Markdown代码已复制到剪贴板');
            }
        }
        
        // 复制图片URL
        function copyImageUrl(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showResult('success', '📋 图片链接已复制');
                });
            } else {
                showResult('info', `图片链接: ${url}`);
            }
        }
        
        // 清空所有
        function clearAll() {
            selectedFiles = [];
            updateUploadButton();
            showImagePreview();
            document.getElementById('uploadResult').innerHTML = '';
            showResult('info', '已清空选择');
        }
        
        // 显示结果消息
        function showResult(type, message) {
            const resultDiv = document.getElementById('uploadResult');
            resultDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            showImagePreview();
            loadUploadedImages();
        });
    </script>
</body>
</html>
