# Waline 部署问题排查清单

## 🔍 问题：配置环境变量后仍然报 "Not initialized" 错误

### 步骤1：验证 LeanCloud 配置

#### 1.1 确认 LeanCloud 版本
❓ **你使用的是哪个版本？**
- [ ] 国际版 (console.leancloud.app) ✅ 推荐
- [ ] 中国版 (console.leancloud.cn) ⚠️ 需要额外配置

如果是中国版，需要额外添加环境变量：
```
LEAN_SERVER = https://你的域名.api.lncldglobal.com
```

#### 1.2 检查应用状态
登录 LeanCloud 控制台，确认：
- [ ] 应用状态是否正常（显示"运行中"）
- [ ] 是否选择了正确的地域（US/HK）

### 步骤2：验证 Vercel 环境变量

#### 2.1 环境变量名称检查
在 Vercel Dashboard → Settings → Environment Variables 中，确认变量名**完全正确**：

```
LEAN_ID          (不是 LEAN_APP_ID 或其他)
LEAN_KEY         (不是 LEAN_APP_KEY 或其他)
LEAN_MASTER_KEY  (不是 LEAN_APP_MASTER_KEY 或其他)
```

⚠️ **常见错误**：
- 变量名拼写错误
- 大小写不对
- 有多余的空格

#### 2.2 环境变量值检查
确认复制的值：
- [ ] 没有多余的空格或换行
- [ ] 完整复制了整个字符串
- [ ] 没有引号包裹

### 步骤3：重新部署检查

#### 3.1 确认重新部署
在 Vercel 中重新部署的正确步骤：

1. **方法一：通过 Deployments**
   - Deployments 页面
   - 找到最新的部署
   - 点击三个点 → Redeploy
   - **选择 "Redeploy with existing Build Cache"**

2. **方法二：触发新部署**
   - 在 GitHub 仓库中做一个小改动
   - 比如修改 README
   - 提交后会自动触发新部署

#### 3.2 查看部署日志
在 Vercel Deployments 中点击最新部署，查看 Function Logs：
- 是否有错误信息
- 是否显示环境变量已加载

### 步骤4：测试不同的数据库方案

如果 LeanCloud 仍然不工作，试试 **Vercel PostgreSQL**：

#### 4.1 创建 PostgreSQL 数据库
1. Vercel Dashboard → Storage
2. Create Database → Postgres
3. 选择你的 Waline 项目连接

#### 4.2 添加必需的环境变量
```
POSTGRES_URL = (自动生成)
JWT_TOKEN = (32位随机字符串)
```

生成 JWT_TOKEN：
```powershell
# PowerShell 命令
-join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | % {[char]$_})
```

### 步骤5：直接测试 API

#### 5.1 测试基础 API
在浏览器访问：
```
https://你的项目.vercel.app/api/
```

应该显示 Waline 版本信息。

#### 5.2 测试评论 API
```
https://你的项目.vercel.app/api/comment?path=/test
```

### 步骤6：使用调试模式

在 Vercel 环境变量中添加：
```
LOG_LEVEL = debug
```

然后重新部署，查看更详细的错误日志。

## 🆘 紧急解决方案

### 方案A：使用 Waline 官方演示服务（临时）
如果急需使用，可以先用官方演示服务：

```yaml
waline:
  serverURL: https://waline.vercel.app
```

⚠️ 注意：这是公共服务，仅用于测试。

### 方案B：重新部署全新实例

1. **删除现有 Vercel 项目**
2. **使用一键部署重新开始**：

   [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample)

3. **部署时直接填写环境变量**

### 方案C：使用其他平台

如果 Vercel 持续有问题，可以尝试：

#### Railway 部署
```bash
# 安装 Railway CLI
npm install -g @railway/cli

# 登录
railway login

# 部署
railway new
```

#### Render 部署
1. 访问 https://render.com
2. New → Web Service
3. 连接 GitHub 仓库
4. 配置环境变量

## 📋 提供诊断信息

为了更好地帮助你，请提供：

### 1. Vercel 部署日志
在 Vercel Dashboard → Functions → Logs 中查看错误详情

### 2. 环境变量截图
Settings → Environment Variables 页面截图（隐藏敏感值）

### 3. LeanCloud 应用信息
- 应用创建时间
- 选择的地域
- 应用版本（国际版/中国版）

### 4. 测试结果
访问以下地址的返回结果：
- `https://你的项目.vercel.app/`
- `https://你的项目.vercel.app/api/`
- `https://你的项目.vercel.app/api/comment`

## 💡 常见原因总结

根据经验，"Not initialized" 错误最常见的原因：

1. **环境变量没有生效**（80% 的情况）
   - 解决：确认重新部署

2. **LeanCloud 地域问题**（10% 的情况）
   - 解决：使用国际版或配置 LEAN_SERVER

3. **变量名称错误**（5% 的情况）
   - 解决：严格按照文档的变量名

4. **网络访问限制**（5% 的情况）
   - 解决：检查 LeanCloud 安全域名设置

---

## 🚀 快速诊断命令

在你的博客目录运行：

```powershell
# 测试 API 是否可访问
Invoke-WebRequest -Uri "https://你的waline项目.vercel.app/api/" -Method GET

# 测试评论接口
Invoke-WebRequest -Uri "https://你的waline项目.vercel.app/api/comment" -Method GET
```

请根据上述清单逐项检查，并提供相关信息，我可以更准确地帮你解决问题。
