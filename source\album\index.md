---
title: 📸 相册集
date: 2025-08-14 14:52:46
type: album
top_img: false
aside: false
comments: true
description: 用镜头记录生活，用心感受美好
---

<div class="album-header">

## 📷 我的相册

<div class="album-intro">
  <p>用镜头记录生活中的美好瞬间，每一张照片都有它独特的故事 ～</p>
</div>

</div>

<div class="album-stats">

### 📊 相册统计

- 📸 **总照片数**：120+ 张
- 🌅 **风景照片**：45 张
- 👥 **人物照片**：30 张
- 🏙️ **城市风光**：25 张
- 🌸 **花卉植物**：20 张

</div>

## 🖼️ 精选作品

{% tabs album %}
<!-- tab 🌅 风景摄影 -->

<div class="gallery-container">
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/landscape1.jpg" alt="日出时分" />
    <div class="gallery-overlay">
      <h4>日出时分</h4>
      <p>清晨的第一缕阳光</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/landscape2.jpg" alt="山间云海" />
    <div class="gallery-overlay">
      <h4>山间云海</h4>
      <p>云雾缭绕的山峰</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/landscape3.jpg" alt="湖光山色" />
    <div class="gallery-overlay">
      <h4>湖光山色</h4>
      <p>宁静的湖面倒影</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/landscape4.jpg" alt="夕阳西下" />
    <div class="gallery-overlay">
      <h4>夕阳西下</h4>
      <p>黄昏时的温暖光线</p>
    </div>
  </div>
</div>

<!-- endtab -->

<!-- tab 🏙️ 城市风光 -->

<div class="gallery-container">
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/city1.jpg" alt="都市夜景" />
    <div class="gallery-overlay">
      <h4>都市夜景</h4>
      <p>霓虹灯下的城市</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/city2.jpg" alt="街头巷尾" />
    <div class="gallery-overlay">
      <h4>街头巷尾</h4>
      <p>城市中的小巷风情</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/city3.jpg" alt="建筑之美" />
    <div class="gallery-overlay">
      <h4>建筑之美</h4>
      <p>现代建筑的线条美</p>
    </div>
  </div>
</div>

<!-- endtab -->

<!-- tab 🌸 花卉植物 -->

<div class="gallery-container">
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/flower1.jpg" alt="樱花盛开" />
    <div class="gallery-overlay">
      <h4>樱花盛开</h4>
      <p>春天的粉色浪漫</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/flower2.jpg" alt="荷花清香" />
    <div class="gallery-overlay">
      <h4>荷花清香</h4>
      <p>夏日池塘的荷花</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/flower3.jpg" alt="秋叶飘零" />
    <div class="gallery-overlay">
      <h4>秋叶飘零</h4>
      <p>秋天的金黄叶片</p>
    </div>
  </div>
</div>

<!-- endtab -->

<!-- tab 📱 生活随拍 -->

<div class="gallery-container">
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/life1.jpg" alt="咖啡时光" />
    <div class="gallery-overlay">
      <h4>咖啡时光</h4>
      <p>午后的悠闲时光</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/life2.jpg" alt="书桌一角" />
    <div class="gallery-overlay">
      <h4>书桌一角</h4>
      <p>学习工作的空间</p>
    </div>
  </div>
  <div class="gallery-item">
    <img src="https://npm.elemecdn.com/anzhiyu-blog@2.0.8/img/post/common/life3.jpg" alt="美食记录" />
    <div class="gallery-overlay">
      <h4>美食记录</h4>
      <p>生活中的小确幸</p>
    </div>
  </div>
</div>

<!-- endtab -->
{% endtabs %}

<div class="photography-tips">

## 📝 摄影心得

### 💡 拍摄技巧

- 🌅 **黄金时刻** - 日出日落时分光线最美
- 📐 **构图法则** - 三分法、对称构图、引导线
- 🎯 **焦点选择** - 突出主体，虚化背景
- 🌈 **色彩搭配** - 注意色彩的和谐与对比
- 📱 **后期处理** - 适度调色，保持自然

### 📷 器材推荐

- **相机**：Canon EOS R6 Mark II
- **镜头**：24-70mm f/2.8、85mm f/1.8
- **配件**：三脚架、偏振镜、ND滤镜
- **手机**：iPhone 15 Pro（日常随拍）

</div>

<style>
.album-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(220, 53, 69, 0.1));
  border-radius: 12px;
}

.album-intro {
  font-size: 1.1rem;
  color: var(--anzhiyu-secondtext);
  margin: 1rem 0;
}

.album-stats {
  background: var(--anzhiyu-card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid var(--anzhiyu-card-border);
}

.album-stats h3 {
  color: var(--anzhiyu-main);
  margin-bottom: 1rem;
}

.album-stats ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.8rem;
}

.album-stats li {
  padding: 0.8rem;
  background: rgba(var(--anzhiyu-main-rgb), 0.05);
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.album-stats li:hover {
  background: rgba(var(--anzhiyu-main-rgb), 0.1);
  transform: translateY(-2px);
}

.gallery-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.gallery-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--anzhiyu-shadow-border);
  transition: all 0.3s ease;
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--anzhiyu-shadow-lightblack);
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 1.5rem 1rem 1rem;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.gallery-overlay p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.photography-tips {
  margin: 3rem 0;
}

.photography-tips h2 {
  color: var(--anzhiyu-main);
  border-left: 4px solid var(--anzhiyu-main);
  padding-left: 1rem;
  margin-bottom: 1.5rem;
}

.photography-tips h3 {
  color: var(--anzhiyu-fontcolor);
  margin: 2rem 0 1rem 0;
  font-size: 1.2rem;
}

.photography-tips ul {
  list-style: none;
  padding: 0;
}

.photography-tips li {
  padding: 0.8rem;
  margin: 0.5rem 0;
  background: var(--anzhiyu-card-bg);
  border-radius: 8px;
  border-left: 4px solid var(--anzhiyu-main);
  transition: all 0.3s ease;
}

.photography-tips li:hover {
  transform: translateX(5px);
  box-shadow: var(--anzhiyu-shadow-border);
}

@media (max-width: 768px) {
  .album-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .album-stats {
    padding: 1rem;
  }

  .album-stats ul {
    grid-template-columns: 1fr;
  }

  .gallery-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .gallery-item img {
    height: 180px;
  }

  .gallery-overlay {
    position: static;
    transform: none;
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem;
  }
}
</style>
