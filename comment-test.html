<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论系统完整测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .status {
            padding: 12px 16px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: 500;
            border-left: 4px solid;
        }
        
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .comment-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #495057;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .comment-list {
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .comment-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .comment-item:last-child {
            border-bottom: none;
        }
        
        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .comment-author {
            font-weight: 600;
            color: #007bff;
        }
        
        .comment-time {
            color: #6c757d;
            font-size: 12px;
        }
        
        .comment-content {
            color: #495057;
            line-height: 1.5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
        }
        
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        .empty {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-error { color: #dc3545; }
        .log-success { color: #28a745; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 评论系统完整测试</h1>
            <p>测试简化版Waline评论服务器的所有功能</p>
        </div>
        
        <div class="content">
            <!-- 服务器状态 -->
            <div class="test-section">
                <h3>🚀 服务器状态检测</h3>
                <div id="server-status" class="status info">正在检测服务器状态...</div>
                <button class="btn" onclick="checkServerStatus()">重新检测</button>
                
                <div class="stats-grid" id="stats-grid" style="display: none;">
                    <div class="stat-card">
                        <div class="stat-number" id="total-comments">0</div>
                        <div class="stat-label">总评论数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="today-comments">0</div>
                        <div class="stat-label">今日评论</div>
                    </div>
                </div>
            </div>
            
            <!-- 提交评论测试 -->
            <div class="test-section">
                <h3>💬 评论提交测试</h3>
                
                <div class="comment-form">
                    <div class="form-group">
                        <label for="nick">昵称 *</label>
                        <input type="text" id="nick" class="form-control" placeholder="请输入您的昵称" value="测试用户">
                    </div>
                    
                    <div class="form-group">
                        <label for="mail">邮箱</label>
                        <input type="email" id="mail" class="form-control" placeholder="可选，填写后可收到回复通知" value="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="link">网站</label>
                        <input type="url" id="link" class="form-control" placeholder="可选，您的个人网站" value="">
                    </div>
                    
                    <div class="form-group">
                        <label for="comment">评论内容 *</label>
                        <textarea id="comment" class="form-control" placeholder="请输入评论内容..." rows="4">这是一条测试评论，用于验证评论系统是否正常工作。</textarea>
                    </div>
                    
                    <button class="btn btn-success" onclick="submitComment()">💬 提交评论</button>
                    <button class="btn" onclick="fillRandomComment()">🎲 随机内容</button>
                </div>
                
                <div id="submit-result"></div>
            </div>
            
            <!-- 评论列表 -->
            <div class="test-section">
                <h3>📝 评论列表</h3>
                <button class="btn" onclick="loadComments()">🔄 刷新评论</button>
                <button class="btn" onclick="clearComments()">🗑️ 清空评论</button>
                
                <div id="comments-container" class="comment-list">
                    <div class="loading">正在加载评论...</div>
                </div>
            </div>
            
            <!-- 压力测试 -->
            <div class="test-section">
                <h3>⚡ 压力测试</h3>
                <p>快速提交多条测试评论，验证服务器稳定性</p>
                <button class="btn" onclick="stressTest(5)">提交5条评论</button>
                <button class="btn" onclick="stressTest(10)">提交10条评论</button>
                <button class="btn" onclick="stressTest(20)">提交20条评论</button>
                
                <div id="stress-result"></div>
            </div>
            
            <!-- 操作日志 -->
            <div class="test-section">
                <h3>📋 操作日志</h3>
                <button class="btn" onclick="clearLogs()">清空日志</button>
                <div id="logs" class="logs"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8360';
        
        // 添加日志
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // 检查服务器状态
        async function checkServerStatus() {
            const statusDiv = document.getElementById('server-status');
            statusDiv.innerHTML = '🔍 正在检测服务器状态...';
            statusDiv.className = 'status info';
            
            try {
                addLog('开始检测服务器状态...');
                
                // 检查服务器基本连接
                const response = await fetch(API_BASE);
                if (response.ok) {
                    statusDiv.innerHTML = '✅ 服务器连接正常';
                    statusDiv.className = 'status success';
                    addLog('服务器连接正常', 'success');
                    
                    // 获取统计信息
                    await loadStats();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ 服务器连接失败: ${error.message}`;
                statusDiv.className = 'status error';
                addLog(`服务器连接失败: ${error.message}`, 'error');
            }
        }
        
        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/comment/count`);
                const data = await response.json();
                
                if (data.errno === 0) {
                    document.getElementById('total-comments').textContent = data.data.comments;
                    document.getElementById('today-comments').textContent = data.data.today;
                    document.getElementById('stats-grid').style.display = 'grid';
                    addLog(`统计信息: 总评论 ${data.data.comments} 条, 今日 ${data.data.today} 条`, 'success');
                }
            } catch (error) {
                addLog(`获取统计信息失败: ${error.message}`, 'error');
            }
        }
        
        // 提交评论
        async function submitComment() {
            const nick = document.getElementById('nick').value.trim();
            const mail = document.getElementById('mail').value.trim();
            const link = document.getElementById('link').value.trim();
            const comment = document.getElementById('comment').value.trim();
            const resultDiv = document.getElementById('submit-result');
            
            if (!nick || !comment) {
                resultDiv.innerHTML = '<div class="status error">❌ 昵称和评论内容不能为空</div>';
                addLog('提交失败: 昵称和评论内容不能为空', 'error');
                return;
            }
            
            resultDiv.innerHTML = '<div class="status info">📤 正在提交评论...</div>';
            addLog(`提交评论: ${nick} - ${comment.substring(0, 30)}...`);
            
            try {
                const response = await fetch(`${API_BASE}/comment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        nick: nick,
                        mail: mail,
                        link: link,
                        comment: comment,
                        url: '/test'
                    })
                });
                
                const data = await response.json();
                
                if (data.errno === 0) {
                    resultDiv.innerHTML = '<div class="status success">✅ 评论提交成功！</div>';
                    addLog(`评论提交成功: ID ${data.data.objectId}`, 'success');
                    
                    // 清空表单
                    document.getElementById('comment').value = '';
                    
                    // 刷新评论列表和统计
                    await loadComments();
                    await loadStats();
                } else {
                    throw new Error(data.errmsg || '提交失败');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ 提交失败: ${error.message}</div>`;
                addLog(`评论提交失败: ${error.message}`, 'error');
            }
        }
        
        // 加载评论列表
        async function loadComments() {
            const container = document.getElementById('comments-container');
            container.innerHTML = '<div class="loading">🔄 正在加载评论...</div>';
            
            try {
                addLog('加载评论列表...');
                const response = await fetch(`${API_BASE}/comment?path=/test&pageSize=50`);
                const data = await response.json();
                
                if (data.errno === 0) {
                    if (data.data.length === 0) {
                        container.innerHTML = '<div class="empty">📝 暂无评论，快来发表第一条评论吧！</div>';
                    } else {
                        container.innerHTML = data.data.map(comment => `
                            <div class="comment-item">
                                <div class="comment-header">
                                    <span class="comment-author">${escapeHtml(comment.nick)}</span>
                                    <span class="comment-time">${new Date(comment.insertedAt).toLocaleString()}</span>
                                </div>
                                <div class="comment-content">${escapeHtml(comment.comment)}</div>
                                ${comment.mail ? `<div class="comment-mail">📧 ${escapeHtml(comment.mail)}</div>` : ''}
                                ${comment.link ? `<div class="comment-link">🔗 <a href="${escapeHtml(comment.link)}" target="_blank">${escapeHtml(comment.link)}</a></div>` : ''}
                            </div>
                        `).join('');
                    }
                    addLog(`加载了 ${data.data.length} 条评论`, 'success');
                } else {
                    throw new Error(data.errmsg || '加载失败');
                }
            } catch (error) {
                container.innerHTML = `<div class="status error">❌ 加载评论失败: ${error.message}</div>`;
                addLog(`加载评论失败: ${error.message}`, 'error');
            }
        }
        
        // 清空评论（仅前端显示）
        function clearComments() {
            const container = document.getElementById('comments-container');
            container.innerHTML = '<div class="empty">📝 评论列表已清空（仅前端显示）</div>';
            addLog('清空了评论列表显示');
        }
        
        // 填充随机评论内容
        function fillRandomComment() {
            const comments = [
                '这个评论系统看起来很不错！',
                '测试一下评论功能是否正常工作。',
                '简化版的服务器运行得很流畅。',
                '界面设计得很美观，用户体验不错。',
                '性能测试通过，响应速度很快。',
                '希望能添加更多功能，比如回复和点赞。',
                '数据持久化功能工作正常。',
                'CORS配置正确，跨域访问没问题。',
                '错误处理机制完善，用户体验友好。',
                '这是一条很长的评论内容，用来测试评论系统对长文本的处理能力，看看是否会出现显示问题或者性能问题。'
            ];
            
            const names = ['小明', '小红', '小李', '小王', '小张', '测试用户', 'Alice', 'Bob', 'Charlie', 'David'];
            
            document.getElementById('nick').value = names[Math.floor(Math.random() * names.length)];
            document.getElementById('comment').value = comments[Math.floor(Math.random() * comments.length)];
            
            addLog('已生成随机评论内容');
        }
        
        // 压力测试
        async function stressTest(count) {
            const resultDiv = document.getElementById('stress-result');
            resultDiv.innerHTML = `<div class="status info">⚡ 开始压力测试，将提交 ${count} 条评论...</div>`;
            
            addLog(`开始压力测试: ${count} 条评论`);
            
            let successCount = 0;
            let failCount = 0;
            const startTime = Date.now();
            
            const promises = [];
            for (let i = 0; i < count; i++) {
                const promise = fetch(`${API_BASE}/comment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        nick: `压力测试用户${i + 1}`,
                        mail: `test${i + 1}@example.com`,
                        comment: `这是第 ${i + 1} 条压力测试评论，时间戳: ${Date.now()}`,
                        url: '/test'
                    })
                }).then(response => response.json()).then(data => {
                    if (data.errno === 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }).catch(() => {
                    failCount++;
                });
                
                promises.push(promise);
            }
            
            try {
                await Promise.all(promises);
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                resultDiv.innerHTML = `
                    <div class="status success">
                        ✅ 压力测试完成！<br>
                        📊 成功: ${successCount} 条<br>
                        ❌ 失败: ${failCount} 条<br>
                        ⏱️ 用时: ${duration}ms<br>
                        📈 平均速度: ${Math.round(count / duration * 1000)} 条/秒
                    </div>
                `;
                
                addLog(`压力测试完成: 成功 ${successCount}, 失败 ${failCount}, 用时 ${duration}ms`, 'success');
                
                // 刷新评论列表和统计
                await loadComments();
                await loadStats();
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ 压力测试失败: ${error.message}</div>`;
                addLog(`压力测试失败: ${error.message}`, 'error');
            }
        }
        
        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 页面加载完成后自动检测
        document.addEventListener('DOMContentLoaded', () => {
            addLog('页面加载完成，开始自动检测...');
            checkServerStatus();
            loadComments();
        });
    </script>
</body>
</html>
