# Vercel Serverless Function 崩溃解决方案

## 🔴 当前错误
```
500: INTERNAL_SERVER_ERROR
Code: FUNCTION_INVOCATION_FAILED
```

这表示 Waline 服务端函数崩溃了，通常是配置问题。

## ✅ 立即解决步骤

### 步骤1：查看 Vercel 错误日志

1. **登录 Vercel Dashboard**
   - https://vercel.com/dashboard

2. **查看函数日志**
   - 点击你的 Waline 项目
   - 点击 "Functions" 标签
   - 点击 "View Logs"
   - 查看最近的错误信息

3. **常见错误信息及解决方法**

   **错误1：`Error: LEAN_ID is required`**
   - 原因：环境变量名称错误或未设置
   - 解决：确认变量名是 `LEAN_ID` 不是 `LEANCLOUD_APP_ID`

   **错误2：`Error: Unauthorized`**
   - 原因：LeanCloud 密钥错误
   - 解决：重新复制密钥，确保没有空格

   **错误3：`Error: connect ETIMEDOUT`**
   - 原因：网络连接问题
   - 解决：检查 LeanCloud 地域设置

### 步骤2：完整重新配置（推荐）

#### 2.1 删除所有现有环境变量
1. Vercel → Settings → Environment Variables
2. 删除所有与 Waline 相关的变量

#### 2.2 重新添加正确的环境变量

**如果使用 LeanCloud 国际版：**
```
LEAN_ID = [你的 AppID]
LEAN_KEY = [你的 AppKey]
LEAN_MASTER_KEY = [你的 MasterKey]
```

**如果使用 LeanCloud 中国版（需要额外变量）：**
```
LEAN_ID = [你的 AppID]
LEAN_KEY = [你的 AppKey]
LEAN_MASTER_KEY = [你的 MasterKey]
LEAN_SERVER = [你的 API 域名]
```

中国版的 LEAN_SERVER 格式：
- 华北节点：`https://[AppID前8位].api.lncldglobal.com`
- 华东节点：`https://[AppID前8位].api.lncldapi.com`

#### 2.3 添加调试变量（临时）
```
LOG_LEVEL = debug
```

### 步骤3：使用 PostgreSQL 替代方案

如果 LeanCloud 持续有问题，改用 Vercel 的 PostgreSQL：

#### 3.1 创建数据库
1. Vercel Dashboard → Storage
2. "Create Database" → 选择 "Postgres"
3. 输入数据库名称，创建

#### 3.2 连接到项目
1. 在数据库页面点击 "Connect Project"
2. 选择你的 Waline 项目
3. 点击 "Connect"

#### 3.3 删除 LeanCloud 变量，添加新变量
```
JWT_TOKEN = [32位随机字符串]
```

生成 JWT_TOKEN：
```powershell
-join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | % {[char]$_})
```

### 步骤4：Fork 并重新部署（最干净的方案）

#### 4.1 Fork Waline 仓库
1. 访问：https://github.com/walinejs/waline
2. 点击 "Fork"
3. Fork 到你的 GitHub 账号

#### 4.2 在 Vercel 重新部署
1. Vercel Dashboard → "New Project"
2. Import 你 Fork 的仓库
3. 选择 `example` 文件夹作为根目录
4. **在部署前**配置环境变量
5. 点击 Deploy

### 步骤5：使用 Railway 部署（备选）

如果 Vercel 持续有问题：

#### 5.1 Railway 一键部署
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template/8fR3sJ)

#### 5.2 配置环境变量
同样添加 LEAN_ID、LEAN_KEY、LEAN_MASTER_KEY

## 🔍 诊断命令

### PowerShell 测试命令
```powershell
# 测试基础 API
$response = Invoke-WebRequest -Uri "https://my-waline-comments-six.vercel.app/api/" -Method GET -ErrorAction SilentlyContinue
if ($response.StatusCode -eq 200) {
    Write-Host "API 正常" -ForegroundColor Green
    $response.Content
} else {
    Write-Host "API 错误" -ForegroundColor Red
}

# 测试评论接口
curl -X GET "https://my-waline-comments-six.vercel.app/api/comment" -H "accept: application/json"
```

## 🎯 验证成功标志

### 1. API 根路径测试
访问：`https://my-waline-comments-six.vercel.app/api/`

成功返回：
```
@waline/server v1.x.x
```

### 2. 评论接口测试
访问：`https://my-waline-comments-six.vercel.app/api/comment`

成功返回：
```json
{
  "errno": 0,
  "errmsg": "",
  "data": {
    "page": 1,
    "totalPages": 0,
    "pageSize": 10,
    "count": 0,
    "data": []
  }
}
```

## 💊 快速修复脚本

创建一个测试文件 `test-waline.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <title>Waline 测试</title>
    <link rel="stylesheet" href="https://unpkg.com/@waline/client@v2/dist/waline.css"/>
</head>
<body>
    <div id="waline"></div>
    <script type="module">
        import { init } from 'https://unpkg.com/@waline/client@v2/dist/waline.mjs';
        
        const waline = init({
            el: '#waline',
            serverURL: 'https://my-waline-comments-six.vercel.app',
            lang: 'zh-CN',
        });
        
        // 检查连接
        fetch('https://my-waline-comments-six.vercel.app/api/')
            .then(res => res.text())
            .then(data => {
                console.log('API Response:', data);
                alert('API 连接成功：' + data);
            })
            .catch(err => {
                console.error('API Error:', err);
                alert('API 连接失败：' + err.message);
            });
    </script>
</body>
</html>
```

用浏览器打开这个文件测试。

## 🆘 终极解决方案

如果以上都不行，使用官方演示服务临时过渡：

```yaml
# _config.anzhiyu.yml
waline:
  serverURL: https://waline.vercel.app  # 官方演示服务
```

然后慢慢调试你自己的部署。

## 📞 获取帮助

1. **Waline 官方讨论区**：https://github.com/walinejs/waline/discussions
2. **Vercel 支持**：https://vercel.com/support
3. **LeanCloud 文档**：https://docs.leancloud.app/

---

**最重要的是**：请先查看 Vercel Functions 的日志，那里会显示具体的错误原因！
