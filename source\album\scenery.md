---
title: 🌅 风景摄影
date: 2025-08-22
type: "album_detail"
top_img: false
aside: false
comments: true
description: 大自然的壮丽美景
---

<div class="album-detail-header">
  <h1>🌅 风景摄影集</h1>
  <p class="album-description">捕捉大自然最美的瞬间，记录山川湖海的壮丽景色</p>
</div>

<div class="photo-grid" id="scenery-gallery">
  <!-- 照片将通过JavaScript动态加载 -->
</div>

<script>
// 风景照片数据
const sceneryPhotos = [
  {
    url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
    title: '雪山日出',
    description: '晨曦中的雪山之巅',
    date: '2025-01',
    location: '瑞士阿尔卑斯'
  },
  {
    url: 'https://images.unsplash.com/photo-1511884642898-4c92249e20b6',
    title: '峡谷奇观',
    description: '大自然的鬼斧神工',
    date: '2024-11',
    location: '美国大峡谷'
  },
  {
    url: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
    title: '热带海滩',
    description: '碧海蓝天椰林风',
    date: '2024-08',
    location: '马尔代夫'
  },
  {
    url: 'https://images.unsplash.com/photo-1433086966358-54859d0ed716',
    title: '森林瀑布',
    description: '林间飞瀑的震撼',
    date: '2024-06',
    location: '九寨沟'
  },
  {
    url: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470',
    title: '湖光山色',
    description: '宁静致远的湖景',
    date: '2024-05',
    location: '新西兰南岛'
  },
  {
    url: 'https://images.unsplash.com/photo-1540390769625-2fc3f8b1d50c',
    title: '极光之夜',
    description: '北极光的绚烂舞蹈',
    date: '2024-03',
    location: '冰岛'
  },
  {
    url: 'https://images.unsplash.com/photo-1532274402911-5a369e4c4bb5',
    title: '沙漠日落',
    description: '金色沙海的余晖',
    date: '2024-02',
    location: '撒哈拉沙漠'
  },
  {
    url: 'https://images.unsplash.com/photo-1518173946687-a4c8892bbd9f',
    title: '云海翻涌',
    description: '山巅之上的云海',
    date: '2023-12',
    location: '黄山'
  },
  {
    url: 'https://images.unsplash.com/photo-1475924156734-496f6cac6ec1',
    title: '秋叶满山',
    description: '层林尽染的秋色',
    date: '2023-10',
    location: '京都'
  }
];

// 动态生成照片网格
function generatePhotoGrid() {
  const gallery = document.getElementById('scenery-gallery');
  
  sceneryPhotos.forEach((photo, index) => {
    const photoItem = document.createElement('div');
    photoItem.className = 'photo-item';
    photoItem.innerHTML = `
      <div class="photo-wrapper">
        <img src="${photo.url}?w=800&q=80" alt="${photo.title}" loading="lazy">
        <div class="photo-info">
          <h3>${photo.title}</h3>
          <p>${photo.description}</p>
          <div class="photo-meta">
            <span class="location">📍 ${photo.location}</span>
            <span class="date">📅 ${photo.date}</span>
          </div>
        </div>
      </div>
    `;
    gallery.appendChild(photoItem);
  });
}

// 页面加载完成后生成相册
document.addEventListener('DOMContentLoaded', generatePhotoGrid);
</script>

<style>
.album-detail-header {
  text-align: center;
  padding: 3rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.album-detail-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

.album-description {
  font-size: 1.2rem;
  opacity: 0.95;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

.photo-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  background: var(--anzhiyu-card-bg);
}

.photo-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.photo-wrapper {
  position: relative;
  overflow: hidden;
}

.photo-wrapper img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-item:hover img {
  transform: scale(1.05);
}

.photo-info {
  padding: 1.2rem;
  background: var(--anzhiyu-card-bg);
}

.photo-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--anzhiyu-fontcolor);
  font-size: 1.2rem;
}

.photo-info p {
  color: var(--anzhiyu-secondtext);
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--anzhiyu-meta-theme-color);
}

.photo-meta span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .album-detail-header h1 {
    font-size: 2rem;
  }
  
  .album-description {
    font-size: 1rem;
  }
  
  .photo-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .photo-wrapper img {
    height: 200px;
  }
}

/* 图片加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.photo-item {
  animation: fadeIn 0.6s ease backwards;
}

.photo-item:nth-child(1) { animation-delay: 0.1s; }
.photo-item:nth-child(2) { animation-delay: 0.2s; }
.photo-item:nth-child(3) { animation-delay: 0.3s; }
.photo-item:nth-child(4) { animation-delay: 0.4s; }
.photo-item:nth-child(5) { animation-delay: 0.5s; }
.photo-item:nth-child(6) { animation-delay: 0.6s; }
.photo-item:nth-child(7) { animation-delay: 0.7s; }
.photo-item:nth-child(8) { animation-delay: 0.8s; }
.photo-item:nth-child(9) { animation-delay: 0.9s; }

/* 深色模式适配 */
[data-theme="dark"] .photo-item {
  background: var(--anzhiyu-card-bg);
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
}

[data-theme="dark"] .photo-info {
  background: var(--anzhiyu-card-bg);
}
</style>
