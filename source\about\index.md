---
title: 关于本人
date: 2025-08-14 14:46:08
comments: false
description: 了解更多关于我的信息
top_img: false
aside: false
type: about
---

<link rel="stylesheet" href="/css/about.css">

<!-- 关于页面主容器 -->
<div class="about-page">
  
  <!-- 头部大横幅区域 -->
  <div class="about-banner-wrapper">
    <div class="about-banner-bg"></div>
    <div class="about-banner-content">
      <div class="banner-avatar">
        <img src="https://i.postimg.cc/Pq3nWf2b/132669446-p0.jpg" alt="郁离">
        <div class="avatar-status"></div>
      </div>
      <div class="banner-info">
        <h1 class="banner-title">Hi, I'm <span class="gradient-text">郁离</span></h1>
        <p class="banner-subtitle">一个热爱技术与生活的前端开发者</p>
        <div class="banner-tags">
          <span class="banner-tag">🚀 前端工程师</span>
          <span class="banner-tag">✨ 开源爱好者</span>
          <span class="banner-tag">📝 技术博主</span>
        </div>
      </div>
      <div class="about-scroll-down">
        <i class="fas fa-angle-down"></i>
      </div>
    </div>
  </div>

  <!-- 统计数据区域 -->
  <div class="about-stats-section">
    <div class="stats-container">
      <div class="stat-item">
        <div class="stat-icon">📝</div>
        <div class="stat-value" data-value="30">0</div>
        <div class="stat-label">文章数</div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">📚</div>
        <div class="stat-value" data-value="15">0</div>
        <div class="stat-label">分类数</div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">🏷️</div>
        <div class="stat-value" data-value="50">0</div>
        <div class="stat-label">标签数</div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">⏰</div>
        <div class="stat-value" data-value="2">0</div>
        <div class="stat-label">运行年</div>
      </div>
    </div>
  </div>

  <!-- 个人介绍区域 -->
  <div class="about-intro-section">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">🎯</span>
        <span>关于我</span>
      </h2>
      <div class="section-line"></div>
    </div>
    <div class="intro-content">
      <p>你好！我是郁离，一名充满热情的前端开发工程师。</p>
      <p>我热爱编程，享受将创意转化为现实的过程。在这里，我分享技术见解、开发经验和生活感悟。</p>
      <p>除了编码，我还喜欢摄影、音乐和阅读。相信技术与艺术的结合能创造更美好的体验。</p>
    </div>
    <div class="social-links">
      <a href="https://github.com/your-username" class="social-link" title="GitHub">
        <i class="fab fa-github"></i>
      </a>
      <a href="mailto:<EMAIL>" class="social-link" title="Email">
        <i class="fas fa-envelope"></i>
      </a>
      <a href="#" class="social-link" title="微信">
        <i class="fab fa-weixin"></i>
      </a>
      <a href="#" class="social-link" title="QQ">
        <i class="fab fa-qq"></i>
      </a>
      <a href="#" class="social-link" title="Twitter">
        <i class="fab fa-twitter"></i>
      </a>
    </div>
  </div>

  <!-- 技能树区域 -->
  <div class="about-skills-section">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">💻</span>
        <span>技能树</span>
      </h2>
      <div class="section-line"></div>
    </div>
    <div class="skills-tabs">
      <div class="skill-tab active" data-tab="frontend">前端技术</div>
      <div class="skill-tab" data-tab="backend">后端技术</div>
      <div class="skill-tab" data-tab="tools">开发工具</div>
      <div class="skill-tab" data-tab="others">其他技能</div>
    </div>
    <div class="skills-content">
      <div class="skill-panel active" id="frontend">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white" alt="HTML5">
            </div>
            <div class="skill-name">HTML5</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 95%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/CSS3-1572B6?style=for-the-badge&logo=css3&logoColor=white" alt="CSS3">
            </div>
            <div class="skill-name">CSS3</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 90%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black" alt="JavaScript">
            </div>
            <div class="skill-name">JavaScript</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 88%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript">
            </div>
            <div class="skill-name">TypeScript</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 80%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB" alt="React">
            </div>
            <div class="skill-name">React</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 85%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Vue.js-35495E?style=for-the-badge&logo=vue.js&logoColor=4FC08D" alt="Vue.js">
            </div>
            <div class="skill-name">Vue.js</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 82%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="backend">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Node.js-43853D?style=for-the-badge&logo=node.js&logoColor=white" alt="Node.js">
            </div>
            <div class="skill-name">Node.js</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 75%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Express.js-404D59?style=for-the-badge" alt="Express">
            </div>
            <div class="skill-name">Express</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 70%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/MySQL-00000F?style=for-the-badge&logo=mysql&logoColor=white" alt="MySQL">
            </div>
            <div class="skill-name">MySQL</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 65%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/MongoDB-4EA94B?style=for-the-badge&logo=mongodb&logoColor=white" alt="MongoDB">
            </div>
            <div class="skill-name">MongoDB</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="tools">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/VS%20Code-0078d7?style=for-the-badge&logo=visual-studio-code&logoColor=white" alt="VS Code">
            </div>
            <div class="skill-name">VS Code</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 95%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Git-F05032?style=for-the-badge&logo=git&logoColor=white" alt="Git">
            </div>
            <div class="skill-name">Git</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 88%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Webpack-8DD6F9?style=for-the-badge&logo=webpack&logoColor=black" alt="Webpack">
            </div>
            <div class="skill-name">Webpack</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 75%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Docker">
            </div>
            <div class="skill-name">Docker</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="skill-panel" id="others">
        <div class="skill-grid">
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Figma-F24E1E?style=for-the-badge&logo=figma&logoColor=white" alt="Figma">
            </div>
            <div class="skill-name">Figma</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 70%"></div>
            </div>
          </div>
          <div class="skill-card">
            <div class="skill-icon">
              <img src="https://img.shields.io/badge/Photoshop-31A8FF?style=for-the-badge&logo=adobe-photoshop&logoColor=white" alt="Photoshop">
            </div>
            <div class="skill-name">Photoshop</div>
            <div class="skill-level">
              <div class="skill-progress" style="width: 65%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 项目经历时间线 -->
  <div class="about-timeline-section">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">📅</span>
        <span>我的历程</span>
      </h2>
      <div class="section-line"></div>
    </div>
    <div class="timeline-wrapper">
      <div class="timeline-line"></div>
      <div class="timeline-item left">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2023 - 至今</div>
          <div class="timeline-title">前端开发工程师</div>
          <div class="timeline-desc">负责公司核心产品的前端开发，优化用户体验，提升页面性能</div>
        </div>
      </div>
      <div class="timeline-item right">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2022</div>
          <div class="timeline-title">个人博客建立</div>
          <div class="timeline-desc">基于Hexo和AnZhiYu主题搭建个人技术博客，分享学习心得</div>
        </div>
      </div>
      <div class="timeline-item left">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2021</div>
          <div class="timeline-title">开始前端之旅</div>
          <div class="timeline-desc">系统学习前端技术栈，参与开源项目，不断提升技术能力</div>
        </div>
      </div>
      <div class="timeline-item right">
        <div class="timeline-dot"></div>
        <div class="timeline-card">
          <div class="timeline-date">2020</div>
          <div class="timeline-title">大学毕业</div>
          <div class="timeline-desc">计算机科学与技术专业，获得学士学位</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 兴趣爱好区域 -->
  <div class="about-hobbies-section">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">🎯</span>
        <span>兴趣爱好</span>
      </h2>
      <div class="section-line"></div>
    </div>
    <div class="hobbies-grid">
      <div class="hobby-card">
        <div class="hobby-icon">💻</div>
        <div class="hobby-title">编程</div>
        <div class="hobby-desc">热爱编码，享受创造的过程</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">📸</div>
        <div class="hobby-title">摄影</div>
        <div class="hobby-desc">用镜头记录生活的美好瞬间</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">🎵</div>
        <div class="hobby-title">音乐</div>
        <div class="hobby-desc">吉他弹唱，用音乐表达情感</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">📚</div>
        <div class="hobby-title">阅读</div>
        <div class="hobby-desc">技术书籍与人文社科并重</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">🏃</div>
        <div class="hobby-title">运动</div>
        <div class="hobby-desc">跑步健身，保持健康活力</div>
      </div>
      <div class="hobby-card">
        <div class="hobby-icon">✈️</div>
        <div class="hobby-title">旅行</div>
        <div class="hobby-desc">探索世界，体验不同文化</div>
      </div>
    </div>
  </div>

  <!-- 联系方式区域 -->
  <div class="about-contact-section">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">📮</span>
        <span>联系我</span>
      </h2>
      <div class="section-line"></div>
    </div>
    <div class="contact-content">
      <p class="contact-intro">如果你有任何问题或建议，欢迎通过以下方式联系我：</p>
      <div class="contact-grid">
        <a href="mailto:<EMAIL>" class="contact-card">
          <div class="contact-icon">📧</div>
          <div class="contact-title">邮箱</div>
          <div class="contact-info"><EMAIL></div>
        </a>
        <a href="https://github.com/your-username" class="contact-card">
          <div class="contact-icon">🐙</div>
          <div class="contact-title">GitHub</div>
          <div class="contact-info">@your-username</div>
        </a>
        <a href="#" class="contact-card">
          <div class="contact-icon">💬</div>
          <div class="contact-title">微信</div>
          <div class="contact-info">your-wechat</div>
        </a>
        <a href="#" class="contact-card">
          <div class="contact-icon">🐧</div>
          <div class="contact-title">QQ</div>
          <div class="contact-info">123456789</div>
        </a>
      </div>
    </div>
  </div>

  <!-- 座右铭区域 -->
  <div class="about-motto-section">
    <div class="motto-card">
      <div class="motto-icon">💭</div>
      <div class="motto-text">"Stay hungry, stay foolish."</div>
      <div class="motto-author">—— Steve Jobs</div>
    </div>
  </div>

</div>

<script>
// 技能标签切换
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('.skill-tab');
  const panels = document.querySelectorAll('.skill-panel');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetTab = this.getAttribute('data-tab');
      
      tabs.forEach(t => t.classList.remove('active'));
      panels.forEach(p => p.classList.remove('active'));
      
      this.classList.add('active');
      document.getElementById(targetTab).classList.add('active');
    });
  });
  
  // 数字动画
  const observerOptions = {
    threshold: 0.5
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const statValues = entry.target.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
          const finalValue = parseInt(stat.getAttribute('data-value'));
          let currentValue = 0;
          const increment = finalValue / 50;
          const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
              currentValue = finalValue;
              clearInterval(timer);
            }
            stat.textContent = Math.floor(currentValue);
          }, 30);
        });
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);
  
  const statsSection = document.querySelector('.about-stats-section');
  if (statsSection) {
    observer.observe(statsSection);
  }
  
  // 滚动箭头
  const scrollDown = document.querySelector('.about-scroll-down');
  if (scrollDown) {
    scrollDown.addEventListener('click', () => {
      window.scrollTo({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    });
  }
});
</script>
