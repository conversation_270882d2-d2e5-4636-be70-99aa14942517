/* ========== 安知鱼关于页面完整样式 ========== */

/* 页面容器 */
.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  animation: fadeInUp 0.6s ease;
}

/* ========== 顶部横幅区域 ========== */
.about-banner {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.4);
}

.about-banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,112C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center;
  opacity: 0.3;
}

.about-banner-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  color: white;
  padding: 2rem;
}

.about-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  animation: slideInDown 0.8s ease;
}

.about-subtitle {
  font-size: 1.5rem;
  opacity: 0.95;
  margin-bottom: 2rem;
  animation: slideInUp 0.8s ease;
}

.about-scroll-down {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
  cursor: pointer;
}

.scroll-icon {
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 25px;
  position: relative;
}

.scroll-icon::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: scrollWheel 2s infinite;
}

/* ========== 个人信息卡片 ========== */
.about-profile-card {
  background: var(--anzhiyu-card-bg);
  border-radius: 20px;
  padding: 3rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 3rem;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.about-profile-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(var(--anzhiyu-main-rgb), 0.05) 0%, transparent 70%);
  animation: rotate 30s linear infinite;
}

.profile-avatar {
  position: relative;
  z-index: 2;
}

.avatar-wrapper {
  position: relative;
  width: 150px;
  height: 150px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--anzhiyu-main);
  box-shadow: 0 10px 30px rgba(var(--anzhiyu-main-rgb), 0.3);
  transition: all 0.3s ease;
}

.avatar-img:hover {
  transform: scale(1.05) rotate(5deg);
}

.avatar-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #44b700;
  border: 3px solid var(--anzhiyu-card-bg);
  border-radius: 50%;
  animation: pulse-status 2s infinite;
}

.profile-info {
  flex: 1;
  position: relative;
  z-index: 2;
}

.profile-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  margin: 0 0 0.5rem 0;
}

.profile-bio {
  font-size: 1.2rem;
  color: var(--anzhiyu-secondtext);
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

.profile-meta {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--anzhiyu-secondtext);
}

.meta-item i {
  color: var(--anzhiyu-main);
  font-size: 1.2rem;
}

.profile-social {
  display: flex;
  gap: 1rem;
}

.social-btn {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--anzhiyu-bg);
  border: 2px solid var(--anzhiyu-card-border);
  color: var(--anzhiyu-fontcolor);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.social-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border-color: var(--anzhiyu-main);
  color: var(--anzhiyu-main);
}

/* ========== 统计数据 ========== */
.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--anzhiyu-card-border);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  margin: 0.5rem 0;
  counter-reset: num var(--num);
}

.stat-number::after {
  content: counter(num);
}

.stat-label {
  color: var(--anzhiyu-secondtext);
  font-size: 1rem;
}

/* ========== 技能展示 ========== */
.about-skills {
  margin-bottom: 3rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  margin: 0 0 1rem 0;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  border-radius: 2px;
}

.section-desc {
  color: var(--anzhiyu-secondtext);
  font-size: 1.1rem;
}

.skills-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.skill-tab {
  padding: 0.8rem 1.5rem;
  background: var(--anzhiyu-card-bg);
  border: 2px solid var(--anzhiyu-card-border);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: var(--anzhiyu-fontcolor);
}

.skill-tab.active {
  background: var(--anzhiyu-main);
  color: white;
  border-color: var(--anzhiyu-main);
}

.skill-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.skills-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1.5rem;
}

.skill-item {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.skill-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--anzhiyu-main-rgb), 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skill-item:hover::before {
  opacity: 1;
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--anzhiyu-main);
}

.skill-icon-wrapper {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.skill-name {
  font-size: 0.9rem;
  color: var(--anzhiyu-fontcolor);
  font-weight: 500;
}

.skill-level {
  margin-top: 0.5rem;
  height: 4px;
  background: var(--anzhiyu-bg);
  border-radius: 2px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  border-radius: 2px;
  transition: width 1s ease;
}

/* ========== 项目展示 ========== */
.about-projects {
  margin-bottom: 3rem;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image {
  transform: scale(1.05);
}

.project-content {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin: 0 0 0.5rem 0;
}

.project-desc {
  color: var(--anzhiyu-secondtext);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.project-tag {
  padding: 0.3rem 0.8rem;
  background: var(--anzhiyu-bg);
  border-radius: 15px;
  font-size: 0.85rem;
  color: var(--anzhiyu-main);
  border: 1px solid var(--anzhiyu-main);
}

/* ========== 时间线 ========== */
.about-timeline {
  margin-bottom: 3rem;
}

.timeline-container {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-content {
  width: calc(50% - 3rem);
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid var(--anzhiyu-card-border);
  position: relative;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.timeline-content::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 20px;
  height: 20px;
  background: var(--anzhiyu-main);
  border: 4px solid var(--anzhiyu-card-bg);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 0 3px var(--anzhiyu-card-border);
}

.timeline-item:nth-child(odd) .timeline-content::after {
  right: -40px;
}

.timeline-item:nth-child(even) .timeline-content::after {
  left: -40px;
}

.timeline-date {
  font-size: 0.9rem;
  color: var(--anzhiyu-main);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin: 0.5rem 0;
}

.timeline-desc {
  color: var(--anzhiyu-secondtext);
  line-height: 1.6;
}

/* ========== 联系方式 ========== */
.about-contact {
  margin-bottom: 3rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.contact-card {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--anzhiyu-main-rgb), 0.1), transparent);
  transition: left 0.5s ease;
}

.contact-card:hover::before {
  left: 100%;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--anzhiyu-main);
}

.contact-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--anzhiyu-main);
}

.contact-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin: 0.5rem 0;
}

.contact-info {
  color: var(--anzhiyu-secondtext);
  font-size: 0.95rem;
}

/* ========== 动画效果 ========== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

@keyframes scrollWheel {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(15px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-status {
  0% {
    box-shadow: 0 0 0 0 rgba(68, 183, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(68, 183, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(68, 183, 0, 0);
  }
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .about-title {
    font-size: 2.5rem;
  }

  .about-subtitle {
    font-size: 1.2rem;
  }

  .about-profile-card {
    flex-direction: column;
    text-align: center;
    padding: 2rem;
  }

  .profile-meta {
    justify-content: center;
  }

  .profile-social {
    justify-content: center;
  }

  .timeline-line {
    left: 20px;
  }

  .timeline-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding-left: 50px;
  }

  .timeline-content {
    width: 100%;
  }

  .timeline-content::after {
    left: -31px !important;
    right: auto !important;
  }

  .skills-content {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }
}

/* ========== 深色模式 ========== */
[data-theme="dark"] .about-banner {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .skill-item,
[data-theme="dark"] .project-card,
[data-theme="dark"] .timeline-content,
[data-theme="dark"] .contact-card {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-card-border);
}

[data-theme="dark"] .skill-tab {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-card-border);
}

[data-theme="dark"] .skill-tab.active {
  background: var(--anzhiyu-main);
  border-color: var(--anzhiyu-main);
}
