const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 内存中存储评论数据
let comments = [];
let commentIdCounter = 1;

// 确保数据目录存在
const dataDir = path.join(__dirname, 'data');
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// 支持的图片格式
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// 数据文件路径
const dataFile = path.join(dataDir, 'comments.json');

// 加载已有评论数据
function loadComments() {
    try {
        if (fs.existsSync(dataFile)) {
            const data = fs.readFileSync(dataFile, 'utf8');
            const parsed = JSON.parse(data);
            comments = parsed.comments || [];
            commentIdCounter = parsed.nextId || 1;
            console.log(`📚 已加载 ${comments.length} 条评论`);
        }
    } catch (error) {
        console.log('📝 创建新的评论数据库');
        comments = [];
        commentIdCounter = 1;
    }
}

// 保存评论数据
function saveComments() {
    try {
        const data = {
            comments: comments,
            nextId: commentIdCounter,
            lastUpdate: new Date().toISOString()
        };
        fs.writeFileSync(dataFile, JSON.stringify(data, null, 2));
    } catch (error) {
        console.error('保存评论失败:', error);
    }
}

// 生成评论ID
function generateId() {
    return commentIdCounter++;
}

// 处理CORS
function setCORSHeaders(res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// 处理GET请求 - 获取评论列表
function handleGetComments(req, res, urlPath) {
    const query = new URLSearchParams(urlPath.query);
    const path = query.get('path') || '/';
    const page = parseInt(query.get('page')) || 1;
    const pageSize = parseInt(query.get('pageSize')) || 10;
    
    // 过滤评论
    const pathComments = comments.filter(comment => comment.url === path);
    
    // 分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageComments = pathComments.slice(start, end);
    
    // 转换为Waline格式
    const walineComments = pageComments.map(comment => ({
        objectId: comment.id.toString(),
        nick: comment.nick,
        mail: comment.mail,
        link: comment.link || '',
        comment: comment.comment,
        url: comment.url,
        insertedAt: comment.insertedAt,
        createdAt: comment.insertedAt,
        updatedAt: comment.insertedAt,
        children: []
    }));
    
    const response = {
        errno: 0,
        errmsg: "",
        data: walineComments,
        count: pathComments.length,
        page: page,
        totalPages: Math.ceil(pathComments.length / pageSize)
    };
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
    
    console.log(`📖 获取评论: ${path} (${pathComments.length}条)`);
}

// 处理POST请求 - 提交评论
function handlePostComment(req, res) {
    let body = '';
    
    req.on('data', chunk => {
        body += chunk.toString();
    });
    
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            
            // 验证必填字段
            if (!data.nick || !data.comment) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    errno: 1,
                    errmsg: "昵称和评论内容不能为空"
                }));
                return;
            }
            
            // 创建新评论
            const newComment = {
                id: generateId(),
                nick: data.nick || '匿名用户',
                mail: data.mail || '',
                link: data.link || '',
                comment: data.comment,
                url: data.url || '/',
                ip: req.connection.remoteAddress || '::1',
                ua: req.headers['user-agent'] || '',
                insertedAt: new Date().toISOString(),
                pid: data.pid || null,
                rid: data.rid || null
            };
            
            // 添加到评论列表
            comments.push(newComment);
            
            // 保存到文件
            saveComments();
            
            // 返回创建的评论（Waline格式）
            const response = {
                errno: 0,
                errmsg: "",
                data: {
                    objectId: newComment.id.toString(),
                    nick: newComment.nick,
                    mail: newComment.mail,
                    link: newComment.link,
                    comment: newComment.comment,
                    url: newComment.url,
                    insertedAt: newComment.insertedAt,
                    createdAt: newComment.insertedAt,
                    updatedAt: newComment.insertedAt
                }
            };
            
            res.writeHead(201, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(response));
            
            console.log(`💬 新评论: ${newComment.nick} - ${newComment.comment.substring(0, 50)}...`);
            
        } catch (error) {
            console.error('处理评论失败:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 1,
                errmsg: "服务器内部错误"
            }));
        }
    });
}

// 生成随机文件名
function generateFileName(originalName) {
    const ext = path.extname(originalName);
    const hash = crypto.randomBytes(16).toString('hex');
    const timestamp = Date.now();
    return `${timestamp}_${hash}${ext}`;
}

// 检查文件类型
function isValidImageType(contentType) {
    return ALLOWED_IMAGE_TYPES.includes(contentType);
}

// 解析multipart/form-data
function parseMultipart(body, boundary) {
    const parts = [];
    const boundaryBuffer = Buffer.from('--' + boundary);
    const endBoundaryBuffer = Buffer.from('--' + boundary + '--');
    
    let start = 0;
    let end = body.indexOf(boundaryBuffer, start);
    
    while (end !== -1) {
        if (start !== 0) {
            const partData = body.slice(start, end);
            const part = parseMultipartPart(partData);
            if (part) parts.push(part);
        }
        
        start = end + boundaryBuffer.length;
        if (body.slice(start, start + 2).toString() === '--') break;
        end = body.indexOf(boundaryBuffer, start);
    }
    
    return parts;
}

// 解析单个multipart部分
function parseMultipartPart(data) {
    const headerEnd = data.indexOf('\r\n\r\n');
    if (headerEnd === -1) return null;
    
    const headerStr = data.slice(0, headerEnd).toString();
    const bodyData = data.slice(headerEnd + 4, data.length - 2); // 移除结尾\r\n
    
    const nameMatch = headerStr.match(/name="([^"]+)"/);
    const filenameMatch = headerStr.match(/filename="([^"]+)"/);
    const contentTypeMatch = headerStr.match(/Content-Type: ([^\r\n]+)/);
    
    if (!nameMatch) return null;
    
    return {
        name: nameMatch[1],
        filename: filenameMatch ? filenameMatch[1] : null,
        contentType: contentTypeMatch ? contentTypeMatch[1] : 'text/plain',
        data: bodyData
    };
}

// 处理图片上传
function handleImageUpload(req, res) {
    let body = Buffer.alloc(0);
    let totalSize = 0;
    
    req.on('data', chunk => {
        totalSize += chunk.length;
        if (totalSize > MAX_FILE_SIZE) {
            res.writeHead(413, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 1,
                errmsg: '文件太大，最大支持 5MB'
            }));
            return;
        }
        body = Buffer.concat([body, chunk]);
    });
    
    req.on('end', () => {
        try {
            const contentType = req.headers['content-type'];
            if (!contentType || !contentType.includes('multipart/form-data')) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    errno: 1,
                    errmsg: '请使用 multipart/form-data 格式上传'
                }));
                return;
            }
            
            const boundary = contentType.split('boundary=')[1];
            if (!boundary) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    errno: 1,
                    errmsg: '缺少 boundary 参数'
                }));
                return;
            }
            
            const parts = parseMultipart(body, boundary);
            const imagePart = parts.find(part => part.filename && isValidImageType(part.contentType));
            
            if (!imagePart) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    errno: 1,
                    errmsg: '未找到有效的图片文件'
                }));
                return;
            }
            
            // 保存图片
            const fileName = generateFileName(imagePart.filename);
            const filePath = path.join(uploadsDir, fileName);
            
            fs.writeFileSync(filePath, imagePart.data);
            
            const imageUrl = `/uploads/${fileName}`;
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 0,
                data: {
                    url: imageUrl,
                    filename: fileName,
                    size: imagePart.data.length,
                    type: imagePart.contentType
                }
            }));
            
            console.log(`🖼️ 图片上传成功: ${fileName} (${Math.round(imagePart.data.length / 1024)}KB)`);
            
        } catch (error) {
            console.error('图片上传失败:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 1,
                errmsg: '图片上传失败'
            }));
        }
    });
}

// 处理静态文件请求
function handleStaticFile(req, res, filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 1,
                errmsg: '文件不存在'
            }));
            return;
        }
        
        const ext = path.extname(filePath).toLowerCase();
        let contentType = 'application/octet-stream';
        
        switch (ext) {
            case '.jpg':
            case '.jpeg':
                contentType = 'image/jpeg';
                break;
            case '.png':
                contentType = 'image/png';
                break;
            case '.gif':
                contentType = 'image/gif';
                break;
            case '.webp':
                contentType = 'image/webp';
                break;
        }
        
        const fileData = fs.readFileSync(filePath);
        res.writeHead(200, { 
            'Content-Type': contentType,
            'Cache-Control': 'public, max-age=31536000' // 1年缓存
        });
        res.end(fileData);
        
    } catch (error) {
        console.error('静态文件请求错误:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            errno: 1,
            errmsg: '文件读取失败'
        }));
    }
}

// 处理GET统计请求
function handleGetStats(req, res) {
    const totalComments = comments.length;
    const todayComments = comments.filter(comment => {
        const today = new Date().toDateString();
        const commentDate = new Date(comment.insertedAt).toDateString();
        return today === commentDate;
    }).length;
    
    // 统计上传的图片数量
    let imageCount = 0;
    try {
        const files = fs.readdirSync(uploadsDir);
        imageCount = files.length;
    } catch (error) {
        console.error('读取上传目录失败:', error);
    }
    
    const response = {
        errno: 0,
        data: {
            comments: totalComments,
            today: todayComments,
            images: imageCount
        }
    };
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    setCORSHeaders(res);
    
    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const urlPath = url.parse(req.url, true);
    const pathname = urlPath.pathname;
    
    try {
        // 路由处理
        if (pathname === '/' && req.method === 'GET') {
            // 首页 - 返回服务器信息
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>简化版 Waline 评论服务器</title>
                    <style>
                        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
                        .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; }
                        .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>🚀 简化版 Waline 评论服务器</h1>
                    <div class="status">
                        ✅ 服务器运行正常
                    </div>
                    <div class="stats">
                        <h3>📊 统计信息</h3>
                        <p>总评论数: ${comments.length}</p>
                        <p>服务器启动时间: ${new Date().toLocaleString()}</p>
                        <p>端口: 8360</p>
                    </div>
                    <h3>📝 API 接口</h3>
                    <ul>
                        <li>GET /comment - 获取评论列表</li>
                        <li>POST /comment - 提交新评论</li>
                        <li>GET /comment/count - 获取统计信息</li>
                    </ul>
                </body>
                </html>
            `);
        } else if (pathname === '/comment' && req.method === 'GET') {
            // 获取评论列表
            handleGetComments(req, res, urlPath);
        } else if (pathname === '/comment' && req.method === 'POST') {
            // 提交新评论
            handlePostComment(req, res);
        } else if (pathname === '/comment/count' && req.method === 'GET') {
            // 获取统计信息
            handleGetStats(req, res);
        } else if (pathname === '/upload' && req.method === 'POST') {
            // 图片上传
            handleImageUpload(req, res);
        } else if (pathname.startsWith('/uploads/') && req.method === 'GET') {
            // 静态文件访问
            const fileName = pathname.replace('/uploads/', '');
            const filePath = path.join(uploadsDir, fileName);
            handleStaticFile(req, res, filePath);
        } else {
            // 404
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                errno: 1,
                errmsg: "接口不存在"
            }));
        }
    } catch (error) {
        console.error('请求处理错误:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            errno: 1,
            errmsg: "服务器内部错误"
        }));
    }
});

// 启动服务器
const PORT = 8360;

// 加载评论数据
loadComments();

server.listen(PORT, () => {
    console.log('🚀 简化版 Waline 评论服务器启动成功!');
    console.log(`📡 服务器地址: http://localhost:${PORT}`);
    console.log(`📊 当前评论数: ${comments.length}`);
    console.log(`💾 数据文件: ${dataFile}`);
    console.log('✅ 服务器已准备好处理评论请求');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n📝 正在保存评论数据...');
    saveComments();
    console.log('👋 服务器已关闭');
    process.exit(0);
});

process.on('SIGTERM', () => {
    saveComments();
    process.exit(0);
});
