{"name": "waline-local-server", "version": "1.0.0", "description": "Local Waline comment server with SQLite storage", "main": "index.js", "scripts": {"start": "node index.js", "simple": "node simple-server.js", "dev": "nodemon index.js", "simple-dev": "nodemon simple-server.js", "reset": "rm -rf data && echo Database reset complete"}, "dependencies": {"@waline/vercel": "^1.30.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}