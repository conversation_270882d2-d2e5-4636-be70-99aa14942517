# 评论系统修复指南

## 🚨 当前状态
- 评论系统已暂时禁用，避免"Failed to fetch"错误
- 需要部署自己的Waline服务器才能正常使用评论功能

## 🛠️ 解决方案

### 方案一：快速部署到 Vercel（推荐）

#### 1. 准备工作
- 注册 GitHub 账号：https://github.com
- 注册 Vercel 账号：https://vercel.com （可用GitHub登录）
- 注册 LeanCloud 账号：https://console.leancloud.cn/

#### 2. LeanCloud 设置
1. 登录 LeanCloud 控制台
2. 点击"创建应用" → 选择"开发版"（免费）
3. 应用名称填写：`waline-comments`
4. 创建后进入应用 → 设置 → 应用凭证
5. 记录以下信息：
   ```
   App ID: xxxxxxxxxx
   App Key: xxxxxxxxxx
   Master Key: xxxxxxxxxx
   ```

#### 3. 部署到 Vercel
**方法A：使用本项目文件**
1. 将项目中的 `waline-deploy` 文件夹上传到你的 GitHub
2. 在 Vercel 中导入该仓库
3. 部署时添加环境变量（见下方）

**方法B：一键部署**
1. 点击链接：https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample
2. 登录 Vercel 并授权 GitHub
3. 项目名称：`my-waline-comments`
4. 点击 "Create" 创建

#### 4. 配置环境变量
在 Vercel 项目设置 → Environment Variables 中添加：
```
LEAN_ID=你的LeanCloud App ID
LEAN_KEY=你的LeanCloud App Key  
LEAN_MASTER_KEY=你的LeanCloud Master Key
SITE_NAME=郁离的博客
SITE_URL=https://你的域名.com
AUTHOR_EMAIL=你的邮箱地址
```

#### 5. 重新部署
配置完环境变量后，在 Vercel 项目页面点击 "Redeploy"

#### 6. 获取服务器地址
部署成功后，你会得到类似 `https://my-waline-comments.vercel.app` 的地址

#### 7. 更新博客配置
编辑 `_config.anzhiyu.yml`：
```yaml
comments:
  use: Waline
  count: true
  card_post_count: true

waline:
  serverURL: https://your-waline.vercel.app  # 替换为你的地址
```

#### 8. 重新生成博客
```bash
hexo clean && hexo generate && hexo server
```

### 方案二：使用其他评论系统

如果Waline部署困难，可以考虑：

#### Twikoo（腾讯云）
```yaml
comments:
  use: Twikoo

twikoo:
  envId: your-env-id
```

#### Artalk（自托管）
```yaml
comments:
  use: Artalk

artalk:
  server: https://your-artalk-server.com
  site: 郁离的博客
```

## 🔧 管理后台

Waline部署成功后：
1. 访问 `https://your-waline.vercel.app/ui`
2. 首次访问注册管理员账号
3. 可以管理评论、用户等

## 📞 需要帮助？

如果遇到问题：
1. 检查 LeanCloud 配置是否正确
2. 确认 Vercel 环境变量设置无误
3. 查看浏览器控制台错误信息
4. 参考官方文档：https://waline.js.org/

## ⚡ 临时解决方案

如果暂时不想部署评论系统，可以：
1. 保持当前配置（评论系统已禁用）
2. 或者使用静态评论方案（如 Giscus）
3. 等待有时间再部署完整的评论系统
