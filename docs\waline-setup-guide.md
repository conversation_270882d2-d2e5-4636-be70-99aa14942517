# Waline 评论系统快速部署指南

## 🚨 当前问题
评论系统显示"Failed to fetch"错误，需要部署自己的Waline服务器。

## 🚀 方法一：使用本项目文件部署到 Vercel

### 步骤1：准备账号
1. 注册 [GitHub](https://github.com) 账号
2. 注册 [Vercel](https://vercel.com) 账号（可用GitHub登录）
3. 注册 [LeanCloud](https://console.leancloud.cn/) 账号

### 步骤2：LeanCloud 配置
1. 登录 LeanCloud 控制台：https://console.leancloud.cn/
2. 创建新应用，选择"开发版"（免费）
3. 进入应用 → 设置 → 应用凭证，记录：
   - `App ID`
   - `App Key`
   - `Master Key`

### 步骤3：部署到 Vercel
1. 将 `waline-deploy` 文件夹上传到你的 GitHub 仓库
2. 在 Vercel 中导入这个仓库
3. 或者直接拖拽 `waline-deploy` 文件夹到 Vercel 部署页面

### 步骤4：一键部署（备选方案）
如果上述方法不行，点击下面的按钮一键部署：

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample)

### 步骤4：配置环境变量
在 Vercel 项目设置中添加以下环境变量：

```
LEAN_ID=你的LeanCloud App ID
LEAN_KEY=你的LeanCloud App Key
LEAN_MASTER_KEY=你的LeanCloud Master Key
SITE_NAME=郁离的博客
SITE_URL=https://你的域名.com
AUTHOR_EMAIL=你的邮箱
```

### 步骤5：重新部署
配置完环境变量后，点击 "Redeploy" 重新部署。

### 步骤6：更新博客配置
将获得的 Vercel 地址（如：`https://your-waline.vercel.app`）更新到 `_config.anzhiyu.yml`：

```yaml
waline:
  serverURL: https://your-waline.vercel.app
```

## 🔧 临时解决方案

如果暂时无法部署自己的服务器，可以使用以下临时服务器：

```yaml
waline:
  serverURL: https://waline-comment-demo.vercel.app
```

## 📧 邮件通知配置（可选）

在 Vercel 环境变量中添加：

```
SMTP_SERVICE=QQ
SMTP_USER=你的QQ邮箱
SMTP_PASS=你的QQ邮箱授权码
```

## 🛠️ 管理后台

访问 `https://your-waline.vercel.app/ui` 进入管理后台：
- 首次访问注册管理员账号
- 管理评论、用户、设置等

## ❓ 常见问题

### 评论无法提交
1. 检查 serverURL 是否正确
2. 确认 LeanCloud 配置是否正确
3. 查看浏览器控制台错误信息

### 评论不显示
1. 确认 Waline 服务正常运行
2. 检查网络连接
3. 清除浏览器缓存

## 📞 获取帮助

如果遇到问题，可以：
1. 查看 [Waline 官方文档](https://waline.js.org/)
2. 在 GitHub 提交 Issue
3. 联系博主获取技术支持
