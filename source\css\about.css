/* ========== 精美关于页面样式 ========== */

/* 基础容器 */
.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  animation: fadeInUp 0.8s ease;
}

/* ========== 顶部横幅区域 ========== */
.about-banner-wrapper {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.about-banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
  25% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
  50% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
  75% { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
}

.about-banner-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.about-banner-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  padding: 2rem;
}

.banner-avatar {
  position: relative;
  margin-bottom: 2rem;
}

.banner-avatar img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 5px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.avatar-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #44b700;
  border: 4px solid white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(68, 183, 0, 0.7); }
  70% { box-shadow: 0 0 0 20px rgba(68, 183, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(68, 183, 0, 0); }
}

.banner-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  animation: slideInDown 1s ease;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #ffd89b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.banner-subtitle {
  font-size: 1.5rem;
  opacity: 0.95;
  margin-bottom: 2rem;
  animation: slideInUp 1s ease;
}

.banner-tags {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  animation: fadeIn 1.5s ease;
}

.banner-tag {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.banner-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.about-scroll-down {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  animation: bounce 2s infinite;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.about-scroll-down:hover {
  opacity: 1;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
  40% { transform: translateX(-50%) translateY(-10px); }
  60% { transform: translateX(-50%) translateY(-5px); }
}

/* ========== 统计数据区域 ========== */
.about-stats-section {
  margin-bottom: 4rem;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--anzhiyu-main);
  margin: 0.5rem 0;
}

.stat-label {
  color: var(--anzhiyu-secondtext);
  font-size: 1rem;
}

/* ========== 区块通用样式 ========== */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  margin: 0 0 1rem 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 2.5rem;
}

.section-line {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  margin: 0 auto;
  border-radius: 2px;
}

/* ========== 个人介绍区域 ========== */
.about-intro-section {
  background: var(--anzhiyu-card-bg);
  padding: 3rem;
  border-radius: 20px;
  margin-bottom: 4rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.intro-content {
  max-width: 800px;
  margin: 0 auto 2rem;
  text-align: center;
}

.intro-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1rem;
  color: var(--anzhiyu-secondtext);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--anzhiyu-bg);
  color: var(--anzhiyu-fontcolor);
  font-size: 1.3rem;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-link:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  background: var(--anzhiyu-main);
  color: white;
}

/* ========== 技能树区域 ========== */
.about-skills-section {
  margin-bottom: 4rem;
}

.skills-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.skill-tab {
  padding: 0.8rem 1.5rem;
  background: var(--anzhiyu-card-bg);
  border: 2px solid var(--anzhiyu-card-border);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: var(--anzhiyu-fontcolor);
}

.skill-tab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.skill-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.skills-content {
  position: relative;
}

.skill-panel {
  display: none;
}

.skill-panel.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.skill-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1.5rem;
}

.skill-card {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.skill-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.skill-card:hover::before {
  left: 100%;
}

.skill-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.skill-icon img {
  max-width: 100%;
  height: auto;
  margin-bottom: 1rem;
}

.skill-name {
  font-size: 1rem;
  color: var(--anzhiyu-fontcolor);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.skill-level {
  height: 6px;
  background: var(--anzhiyu-bg);
  border-radius: 3px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 1s ease;
  animation: progressLoad 1s ease;
}

@keyframes progressLoad {
  from { width: 0; }
}

/* ========== 时间线区域 ========== */
.about-timeline-section {
  margin-bottom: 4rem;
}

.timeline-wrapper {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #667eea, #764ba2);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
}

.timeline-item.left {
  flex-direction: row;
}

.timeline-item.right {
  flex-direction: row-reverse;
}

.timeline-dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #667eea;
  border: 4px solid var(--anzhiyu-card-bg);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.timeline-card {
  width: calc(50% - 3rem);
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.timeline-card:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.timeline-date {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin: 0.5rem 0;
}

.timeline-desc {
  color: var(--anzhiyu-secondtext);
  line-height: 1.6;
}

/* ========== 兴趣爱好区域 ========== */
.about-hobbies-section {
  margin-bottom: 4rem;
}

.hobbies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.hobby-card {
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.hobby-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s ease;
  opacity: 0;
}

.hobby-card:hover::before {
  opacity: 1;
}

.hobby-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.hobby-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  transition: all 0.3s ease;
}

.hobby-card:hover .hobby-icon {
  transform: scale(1.2) rotate(5deg);
}

.hobby-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin-bottom: 0.5rem;
}

.hobby-desc {
  color: var(--anzhiyu-secondtext);
  font-size: 0.95rem;
}

/* ========== 联系方式区域 ========== */
.about-contact-section {
  margin-bottom: 4rem;
}

.contact-content {
  text-align: center;
}

.contact-intro {
  font-size: 1.1rem;
  color: var(--anzhiyu-secondtext);
  margin-bottom: 2rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.contact-card {
  background: var(--anzhiyu-card-bg);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  display: block;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.contact-card:hover::before {
  left: 100%;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.contact-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.contact-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--anzhiyu-fontcolor);
  margin: 0.5rem 0;
}

.contact-info {
  color: var(--anzhiyu-secondtext);
  font-size: 0.95rem;
}

/* ========== 座右铭区域 ========== */
.about-motto-section {
  margin-bottom: 4rem;
}

.motto-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  color: white;
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.motto-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.motto-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  position: relative;
  z-index: 1;
}

.motto-text {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.motto-author {
  font-size: 1.1rem;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

/* ========== 动画效果 ========== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .about-banner-wrapper {
    height: 400px;
  }

  .banner-title {
    font-size: 2.5rem;
  }

  .banner-subtitle {
    font-size: 1.2rem;
  }

  .banner-avatar img {
    width: 120px;
    height: 120px;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .skill-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .timeline-line {
    left: 20px;
  }

  .timeline-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding-left: 50px;
  }

  .timeline-dot {
    left: 20px;
  }

  .timeline-card {
    width: 100%;
  }

  .hobbies-grid,
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 2rem;
  }

  .motto-text {
    font-size: 1.4rem;
  }
}

/* ========== 深色模式适配 ========== */
[data-theme="dark"] .about-banner-bg {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

[data-theme="dark"] .stat-item,
[data-theme="dark"] .about-intro-section,
[data-theme="dark"] .skill-card,
[data-theme="dark"] .skill-tab,
[data-theme="dark"] .timeline-card,
[data-theme="dark"] .hobby-card,
[data-theme="dark"] .contact-card {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-card-border);
}

[data-theme="dark"] .social-link {
  background: var(--anzhiyu-card-bg);
}

[data-theme="dark"] .skill-level {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .motto-card {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}
