/* 评论系统自定义样式 */

/* 评论区整体样式 */
.waline {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  box-shadow: var(--anzhiyu-shadow-border);
  transition: all 0.3s ease;
}

/* 评论输入框样式 */
.wl-editor {
  border-radius: 8px;
  border: 2px solid var(--anzhiyu-card-border);
  transition: all 0.3s ease;
  background: var(--anzhiyu-bg);
}

.wl-editor:focus-within {
  border-color: var(--anzhiyu-main);
  box-shadow: 0 0 0 3px rgba(var(--anzhiyu-main-rgb), 0.1);
}

/* 评论输入区域 */
.wl-input {
  background: transparent;
  color: var(--anzhiyu-fontcolor);
  border: none;
  resize: vertical;
  min-height: 120px;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.6;
}

.wl-input::placeholder {
  color: var(--anzhiyu-secondtext);
  opacity: 0.8;
}

/* 评论工具栏 */
.wl-panel {
  background: var(--anzhiyu-card-bg);
  border-top: 1px solid var(--anzhiyu-card-border);
  padding: 0.8rem 1rem;
  border-radius: 0 0 8px 8px;
}

/* 表情按钮 */
.wl-emoji {
  color: var(--anzhiyu-secondtext);
  transition: all 0.3s ease;
}

.wl-emoji:hover {
  color: var(--anzhiyu-main);
  transform: scale(1.1);
}

/* 提交按钮 */
.wl-btn {
  background: linear-gradient(135deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1.2rem;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(var(--anzhiyu-main-rgb), 0.3);
}

.wl-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--anzhiyu-main-rgb), 0.4);
}

.wl-btn:active {
  transform: translateY(0);
}

/* 用户信息输入框 */
.wl-input-wrapper input {
  background: var(--anzhiyu-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 6px;
  padding: 0.5rem 0.8rem;
  color: var(--anzhiyu-fontcolor);
  transition: all 0.3s ease;
}

.wl-input-wrapper input:focus {
  border-color: var(--anzhiyu-main);
  box-shadow: 0 0 0 2px rgba(var(--anzhiyu-main-rgb), 0.1);
  outline: none;
}

/* 评论列表 */
.wl-cards {
  margin-top: 2rem;
}

/* 单条评论 */
.wl-card {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 10px;
  padding: 1.2rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.wl-card:hover {
  box-shadow: var(--anzhiyu-shadow-lightblack);
  transform: translateY(-2px);
}

/* 评论头像 */
.wl-avatar {
  border-radius: 50%;
  border: 2px solid var(--anzhiyu-card-border);
  transition: all 0.3s ease;
}

.wl-avatar:hover {
  border-color: var(--anzhiyu-main);
  transform: scale(1.05);
}

/* 评论用户名 */
.wl-nick {
  color: var(--anzhiyu-main);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.wl-nick:hover {
  color: var(--anzhiyu-main-light);
}

/* 评论时间 */
.wl-time {
  color: var(--anzhiyu-secondtext);
  font-size: 12px;
}

/* 评论内容 */
.wl-content {
  color: var(--anzhiyu-fontcolor);
  line-height: 1.6;
  margin: 0.8rem 0;
}

.wl-content p {
  margin: 0.5rem 0;
}

.wl-content code {
  background: var(--anzhiyu-code-bg);
  color: var(--anzhiyu-code-color);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.9em;
}

.wl-content pre {
  background: var(--anzhiyu-code-bg);
  border-radius: 6px;
  padding: 1rem;
  overflow-x: auto;
}

/* 评论操作按钮 */
.wl-action {
  color: var(--anzhiyu-secondtext);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
}

.wl-action:hover {
  color: var(--anzhiyu-main);
  background: rgba(var(--anzhiyu-main-rgb), 0.1);
}

/* 回复评论样式 */
.wl-quote {
  background: var(--anzhiyu-bg);
  border-left: 3px solid var(--anzhiyu-main);
  padding: 0.8rem;
  margin: 0.5rem 0;
  border-radius: 0 6px 6px 0;
}

/* 加载更多按钮 */
.wl-load-more {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  color: var(--anzhiyu-fontcolor);
  border-radius: 6px;
  padding: 0.8rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 1rem auto;
}

.wl-load-more:hover {
  background: var(--anzhiyu-main);
  color: white;
  border-color: var(--anzhiyu-main);
}

/* 评论统计 */
.wl-count {
  color: var(--anzhiyu-secondtext);
  font-size: 14px;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed var(--anzhiyu-card-border);
}

/* 表情面板 */
.wl-emoji-popup {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-card-border);
  border-radius: 8px;
  box-shadow: var(--anzhiyu-shadow-black);
  padding: 0.5rem;
}

.wl-emoji-popup .wl-tab {
  background: var(--anzhiyu-bg);
  border-radius: 4px;
  margin: 0.2rem;
  transition: all 0.3s ease;
}

.wl-emoji-popup .wl-tab:hover {
  background: var(--anzhiyu-main);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .waline {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 8px;
  }
  
  .wl-input {
    min-height: 100px;
    padding: 0.8rem;
  }
  
  .wl-card {
    padding: 1rem;
  }
  
  .wl-panel {
    padding: 0.6rem 0.8rem;
  }
}

/* 深色模式适配 */
[data-theme="dark"] .waline {
  background: var(--anzhiyu-card-bg);
}

[data-theme="dark"] .wl-editor {
  background: var(--anzhiyu-bg);
  border-color: var(--anzhiyu-card-border);
}

[data-theme="dark"] .wl-input {
  color: var(--anzhiyu-fontcolor);
}

/* 评论区动画效果 */
.wl-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特殊用户标识 */
.wl-badge {
  background: linear-gradient(135deg, var(--anzhiyu-main), var(--anzhiyu-main-light));
  color: white;
  font-size: 10px;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  margin-left: 0.5rem;
  font-weight: 500;
}
