# Waline 评论系统部署指南

## 简介

Waline 是一个简洁、安全的评论系统，支持多种部署方式。本指南将介绍如何快速部署 Waline 评论系统。

## 方案一：Vercel 部署（推荐）

### 1. 准备工作

- GitHub 账号
- Vercel 账号（可用 GitHub 登录）
- LeanCloud 账号（用于数据存储）

### 2. LeanCloud 设置

1. 访问 [LeanCloud](https://console.leancloud.cn/) 并注册账号
2. 创建新应用，选择"开发版"（免费）
3. 进入应用 -> 设置 -> 应用凭证，记录：
   - `App ID`
   - `App Key`
   - `Master Key`

### 3. Vercel 一键部署

1. 点击下面的按钮一键部署：

   [![Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample)

2. 登录 Vercel 并授权 GitHub
3. 填写项目名称（如：`my-waline`）
4. 点击 "Create" 创建项目

### 4. 环境变量配置

在 Vercel 项目设置中添加环境变量：

```
LEAN_ID=你的LeanCloud App ID
LEAN_KEY=你的LeanCloud App Key
LEAN_MASTER_KEY=你的LeanCloud Master Key
SITE_NAME=你的网站名称
SITE_URL=你的网站地址
AUTHOR_EMAIL=你的邮箱
```

### 5. 重新部署

配置完环境变量后，在 Vercel 项目页面点击 "Redeploy" 重新部署。

### 6. 获取服务器地址

部署成功后，你会得到一个类似 `https://your-project.vercel.app` 的地址，这就是你的 Waline 服务器地址。

## 方案二：Railway 部署

1. 访问 [Railway](https://railway.app/)
2. 使用 GitHub 登录
3. 点击 "New Project" -> "Deploy from GitHub repo"
4. 选择 Waline 模板或 fork 官方仓库
5. 配置环境变量（同 Vercel）
6. 部署完成

## 方案三：自建服务器部署

### 使用 Docker

```bash
# 创建 docker-compose.yml
version: '3'
services:
  waline:
    image: lizheming/waline:latest
    container_name: waline
    restart: unless-stopped
    ports:
      - "8360:8360"
    environment:
      - LEAN_ID=你的LeanCloud App ID
      - LEAN_KEY=你的LeanCloud App Key
      - LEAN_MASTER_KEY=你的LeanCloud Master Key
      - SITE_NAME=你的网站名称
      - SITE_URL=你的网站地址
    volumes:
      - ./data:/app/data

# 启动服务
docker-compose up -d
```

### 使用 Node.js

```bash
# 克隆项目
git clone https://github.com/walinejs/waline.git
cd waline/example

# 安装依赖
npm install

# 设置环境变量
export LEAN_ID=你的LeanCloud App ID
export LEAN_KEY=你的LeanCloud App Key
export LEAN_MASTER_KEY=你的LeanCloud Master Key

# 启动服务
npm start
```

## 博客配置

在你的 `_config.anzhiyu.yml` 中配置：

```yaml
comments:
  use: Waline
  text: true
  lazyload: false
  count: true
  card_post_count: true

waline:
  serverURL: https://your-waline-server.vercel.app
  bg: /img/comment_bg.png
  pageview: true
  meta_css: true
  imageUploader: true
  option:
    lang: zh-CN
    locale:
      placeholder: '欢迎留言！支持 Markdown 语法 ～'
    emoji:
      - https://unpkg.com/@waline/emojis@1.1.0/weibo
      - https://unpkg.com/@waline/emojis@1.1.0/bilibili
    meta: ['nick', 'mail', 'link']
    requiredMeta: ['nick', 'mail']
    wordLimit: [0, 1000]
    pageSize: 10
```

## 管理后台

访问 `https://your-waline-server.vercel.app/ui` 进入管理后台：

1. 首次访问会要求注册管理员账号
2. 可以管理评论、用户、设置等
3. 支持评论审核、垃圾评论过滤等功能

## 高级配置

### 邮件通知

在环境变量中添加：

```
SMTP_SERVICE=QQ
SMTP_USER=你的QQ邮箱
SMTP_PASS=你的QQ邮箱授权码
SITE_NAME=你的网站名称
SITE_URL=你的网站地址
AUTHOR_EMAIL=你的邮箱
```

### 微信通知

```
SC_KEY=你的Server酱密钥
```

### 自定义域名

1. 在 Vercel 项目设置中添加自定义域名
2. 配置 DNS 解析
3. 更新博客配置中的 `serverURL`

## 常见问题

### 1. 评论不显示

- 检查 `serverURL` 是否正确
- 确认 Waline 服务是否正常运行
- 查看浏览器控制台是否有错误

### 2. 无法发送评论

- 检查 LeanCloud 配置是否正确
- 确认环境变量是否设置正确
- 查看 Waline 服务日志

### 3. 邮件通知不工作

- 检查 SMTP 配置是否正确
- 确认邮箱授权码是否有效
- 查看 Waline 服务日志

## 参考链接

- [Waline 官方文档](https://waline.js.org/)
- [LeanCloud 控制台](https://console.leancloud.cn/)
- [Vercel 官网](https://vercel.com/)
- [Railway 官网](https://railway.app/)

---

配置完成后，重新生成博客并访问留言板页面即可看到评论系统！
