# Waline 评论系统完整部署指南

> Waline 是一款基于 Valine 衍生的带后端评论系统，功能完善，部署简单，是目前最推荐的评论系统。

## 📋 前置准备

1. **GitHub 账号**（用于 Vercel 部署）
2. **数据库选择**（以下选择其一）：
   - LeanCloud 国际版（推荐，免费）
   - PostgreSQL（Vercel 提供）
   - MySQL/SQLite（自建）

## 🚀 部署步骤

### 第一步：部署 Waline 服务端

#### 方案一：Vercel 部署（推荐）

1. **点击一键部署**
   - 访问：https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample
   - 或访问：https://github.com/walinejs/waline/tree/main/example

2. **配置环境变量**
   ```
   必需配置：
   - LEAN_ID: LeanCloud 应用 ID
   - LEAN_KEY: LeanCloud 应用 Key
   - LEAN_MASTER_KEY: LeanCloud 应用 Master Key
   
   可选配置：
   - SITE_NAME: 网站名称
   - SITE_URL: 网站地址
   - SECURE_DOMAINS: 安全域名（你的博客域名）
   ```

3. **点击 Deploy 部署**
   - 等待部署完成（约 1-2 分钟）
   - 记录下分配的域名：`https://your-project.vercel.app`

#### 方案二：Railway 部署（备选）

1. **访问 Railway**
   - 地址：https://railway.app/
   - 点击 "Start a New Project"

2. **部署 Waline**
   - 选择 "Deploy from GitHub repo"
   - 搜索 `waline`
   - 选择官方仓库部署

3. **配置环境变量**（同 Vercel）

### 第二步：配置数据库

#### LeanCloud 配置（推荐）

1. **注册 LeanCloud 国际版**
   - 访问：https://console.leancloud.app/
   - 注册账号（建议使用国际版，无需备案）

2. **创建应用**
   ```
   - 应用名称：你的博客名称
   - 应用类型：开发版（免费）
   - 地域：选择离你最近的节点
   ```

3. **获取密钥**
   - 进入应用 → 设置 → 应用凭证
   - 记录以下信息：
     - AppID（LEAN_ID）
     - AppKey（LEAN_KEY）  
     - MasterKey（LEAN_MASTER_KEY）

4. **配置安全域名**
   - 设置 → 安全中心 → Web 安全域名
   - 添加你的博客域名和 Vercel 域名

### 第三步：博客配置

在 `_config.anzhiyu.yml` 中配置：

```yaml
# 评论系统
comments:
  use: Waline # 使用 Waline
  text: true
  lazyload: false
  count: true
  card_post_count: true

# Waline 配置
waline:
  serverURL: https://your-waline.vercel.app # 你的 Waline 服务端地址
  bg: /img/comment_bg.png # 评论框背景图
  pageview: true # 文章访问量统计
  option:
    lang: zh-CN # 语言
    locale: # 自定义语言配置
      placeholder: '欢迎留言！支持 Markdown 语法 ～'
    emoji: # 表情包配置
      - https://unpkg.com/@waline/emojis@1.2.0/weibo
      - https://unpkg.com/@waline/emojis@1.2.0/alus
      - https://unpkg.com/@waline/emojis@1.2.0/bilibili
      - https://unpkg.com/@waline/emojis@1.2.0/qq
      - https://unpkg.com/@waline/emojis@1.2.0/tieba
    meta: ['nick', 'mail', 'link'] # 评论者信息
    requiredMeta: ['nick', 'mail'] # 必填项
    wordLimit: [0, 500] # 字数限制
    pageSize: 10 # 每页评论数
    imageUploader: true # 开启图片上传
    highlighter: true # 代码高亮
    texRenderer: false # 数学公式
    search: false # 评论搜索
    pageview: true # 访问量统计
    comment: true # 评论数统计
    copyright: false # 版权信息
```

### 第四步：高级配置

#### 1. 配置邮件通知

在 Vercel 环境变量中添加：

```env
# SMTP 邮件配置
SMTP_SERVICE=QQ # 邮件服务商
SMTP_USER=<EMAIL> # 邮箱账号
SMTP_PASS=your-password # 邮箱密码/授权码
SITE_NAME=你的博客名称
SITE_URL=https://your-blog.com
AUTHOR_EMAIL=<EMAIL> # 博主邮箱
```

#### 2. 配置评论审核

```env
# 开启评论审核
COMMENT_AUDIT=true
# Akismet 反垃圾（可选）
AKISMET_KEY=your-akismet-key
```

#### 3. 配置 Telegram 通知

```env
TG_BOT_TOKEN=your-bot-token
TG_CHAT_ID=your-chat-id
```

### 第五步：管理后台

1. **访问管理后台**
   - 地址：`https://your-waline.vercel.app/ui`
   - 首次访问需要注册管理员账号

2. **管理员功能**
   - 评论管理（审核、删除、置顶）
   - 用户管理
   - 数据统计
   - 系统设置

### 第六步：自定义样式

在主题的 `source/css/_custom/custom.css` 中添加：

```css
/* Waline 评论框自定义样式 */
.wl-cards .wl-item {
  padding: 1rem;
  border-radius: 8px;
  background: var(--anzhiyu-card-bg);
  margin-bottom: 1rem;
}

/* 评论者头像圆角 */
.wl-cards .wl-user img {
  border-radius: 50%;
}

/* 评论输入框 */
.wl-editor {
  background: var(--anzhiyu-secondbg);
  border-radius: 12px;
}

/* 表情面板 */
.wl-emoji-popup {
  background: var(--anzhiyu-maskbg);
  backdrop-filter: blur(20px);
  border-radius: 12px;
}
```

## 🎨 个性化配置

### 自定义表情包

```yaml
waline:
  option:
    emoji:
      - https://unpkg.com/@waline/emojis@1.2.0/weibo # 微博表情
      - https://unpkg.com/@waline/emojis@1.2.0/bilibili # B站表情
      - https://unpkg.com/@waline/emojis@1.2.0/qq # QQ表情
      - https://unpkg.com/@waline/emojis@1.2.0/tw-emoji # Twitter Emoji
      # 自定义表情包 JSON 格式
      - https://your-domain.com/custom-emoji.json
```

### 自定义欢迎语

```yaml
waline:
  option:
    locale:
      placeholder: '欢迎留言！支持 Markdown 语法 ～\n填写QQ邮箱可自动获取QQ头像'
      sofa: '🎉 快来做第一个评论的人吧～'
      replyPlaceholder: '善语结善缘，恶言伤人心 ～'
```

### 评论等级系统

```yaml
waline:
  option:
    locale:
      level0: '潜水' # 0 条评论
      level1: '冒泡' # 1 条评论
      level2: '吐槽' # 5 条评论
      level3: '活跃' # 10 条评论
      level4: '话唠' # 30 条评论
      level5: '传说' # 100 条评论
```

## ⚠️ 常见问题

### 1. 评论无法显示
- 检查 serverURL 是否正确
- 检查 LeanCloud 安全域名配置
- 查看浏览器控制台错误信息

### 2. 无法上传图片
- 确保 `imageUploader: true`
- 检查图床配置
- 验证 CORS 设置

### 3. 邮件通知不工作
- 检查 SMTP 配置
- QQ 邮箱需要使用授权码而非密码
- 确保发件邮箱开启了 SMTP 服务

### 4. 访问统计不准确
- 清除浏览器缓存
- 检查 `pageview: true` 配置
- 确保每个页面的 path 唯一

## 📊 数据备份

### 导出评论数据

1. 登录 LeanCloud 控制台
2. 进入数据存储 → 导出
3. 选择 Comment 表导出

### 定期备份脚本

```javascript
// backup.js
const AV = require('leancloud-storage');

AV.init({
  appId: 'your-app-id',
  appKey: 'your-app-key',
  masterKey: 'your-master-key'
});

// 导出评论数据
const query = new AV.Query('Comment');
query.limit(1000);
query.find().then(comments => {
  console.log(JSON.stringify(comments));
});
```

## 🔒 安全建议

1. **设置安全域名**：只允许你的博客域名访问
2. **开启评论审核**：防止垃圾评论
3. **配置敏感词过滤**：自动屏蔽不当内容
4. **定期备份数据**：防止数据丢失
5. **使用 HTTPS**：确保数据传输安全

## 📚 相关资源

- [Waline 官方文档](https://waline.js.org/)
- [LeanCloud 文档](https://docs.leancloud.app/)
- [Vercel 部署指南](https://vercel.com/docs)
- [表情包资源](https://github.com/walinejs/emojis)

## 💡 优化建议

1. **CDN 加速**：将 Waline 客户端 JS 放到 CDN
2. **图片压缩**：配置图片自动压缩
3. **懒加载**：评论区滚动到可见区域才加载
4. **缓存策略**：合理设置缓存时间

---

配置完成后，运行 `hexo clean && hexo g && hexo s` 查看效果！
