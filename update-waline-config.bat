@echo off
echo ========================================
echo     Waline 配置更新脚本
echo ========================================
echo.

set /p SERVER_URL="请输入您的 Waline 服务器地址 (例如: https://my-waline.vercel.app): "

if "%SERVER_URL%"=="" (
    echo 错误：服务器地址不能为空！
    pause
    exit /b 1
)

echo.
echo 正在更新配置文件...

powershell -Command "(Get-Content '_config.anzhiyu.yml') -replace 'serverURL: https://your-waline-server.vercel.app', 'serverURL: %SERVER_URL%' | Set-Content '_config.anzhiyu.yml'"

echo 配置更新完成！
echo.
echo 新的服务器地址: %SERVER_URL%
echo.
echo 接下来请运行以下命令重新生成博客：
echo   hexo clean
echo   hexo generate  
echo   hexo server
echo.
pause
