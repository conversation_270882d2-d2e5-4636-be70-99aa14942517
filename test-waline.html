<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waline 评论系统测试页面</title>
    <link rel="stylesheet" href="https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .status {
            padding: 12px 16px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: 500;
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        #waline-test {
            margin-top: 20px;
            min-height: 300px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-box {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .info-box h4 {
            margin-top: 0;
            color: #495057;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-error { color: #dc3545; }
        .log-success { color: #28a745; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Waline 评论系统测试</h1>
        
        <div id="status" class="status info">正在测试连接...</div>
        
        <h2>服务器信息</h2>
        <ul>
            <li><strong>服务器地址:</strong> http://localhost:8360</li>
            <li><strong>测试时间:</strong> <span id="test-time"></span></li>
        </ul>
        
        <h2>连接测试</h2>
        <button onclick="testConnection()">测试服务器连接</button>
        <div id="connection-result"></div>
        
        <h2>评论组件测试</h2>
        <div id="waline-test"></div>
    </div>

    <script type="module">
        // 显示当前时间
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        
        // 测试服务器连接
        window.testConnection = async function() {
            const resultDiv = document.getElementById('connection-result');
            const statusDiv = document.getElementById('status');
            
            try {
                resultDiv.innerHTML = '<div class="status info">正在测试连接...</div>';
                
                const response = await fetch('http://localhost:8360');
                const text = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="status success">✅ 服务器连接成功！</div>';
                    statusDiv.innerHTML = '✅ Waline 服务器运行正常';
                    statusDiv.className = 'status success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ 连接失败: ${error.message}</div>`;
                statusDiv.innerHTML = '❌ Waline 服务器连接失败';
                statusDiv.className = 'status error';
            }
        };
        
        // 初始化 Waline
        try {
            const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js');
            
            const waline = init({
                el: '#waline-test',
                serverURL: 'http://localhost:8360',
                path: '/test',
                lang: 'zh-CN',
                locale: {
                    placeholder: '这是一个测试页面，请随意留言测试评论功能...'
                }
            });
            
            document.getElementById('status').innerHTML = '✅ Waline 组件加载成功';
            document.getElementById('status').className = 'status success';
            
        } catch (error) {
            document.getElementById('status').innerHTML = `❌ Waline 组件加载失败: ${error.message}`;
            document.getElementById('status').className = 'status error';
            console.error('Waline 初始化失败:', error);
        }
        
        // 自动测试连接
        testConnection();
    </script>
</body>
</html>
