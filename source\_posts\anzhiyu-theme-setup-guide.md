---

title: AnZhiYu主题完整配置指南
date: 2025-08-18 21:45:00
tags:
  - Hexo
  - AnZhiYu
  - 博客
categories:
  - 技术分享
  - 前端开发
cover: https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=1200
ai: true
swiper_index: 1
top_group_index: 1
description: 详细介绍如何配置AnZhiYu主题，实现与官方演示站点一致的功能和外观
abbrlink: anzhiyu-setup
sticky: 100
---


## 前言

AnZhiYu 是一个功能丰富、外观精美的 Hexo 主题，本文将详细介绍如何配置各项功能。

## 主要功能

### 1. AI 摘要功能

AnZhiYu 主题支持两种 AI 摘要模式：

- **tianli 模式**：使用天理 GPT 服务
- **local 模式**：本地配置摘要内容

在文章的 front-matter 中添加 `ai: true` 即可启用 AI 摘要功能。

### 2. 音乐馆

音乐馆支持多个音乐平台：

- 腾讯音乐 (tencent)
- 网易云音乐 (netease)
- QQ音乐 (qq)

只需在菜单配置中添加歌单 ID 和平台参数即可。

### 3. 首页轮播

通过在文章 front-matter 中设置：

- `swiper_index`: 控制在轮播中的显示
- `top_group_index`: 控制排序

### 4. 留言板

使用信封样式的留言板，提供优雅的交互体验。

### 5. 小空调

一个有趣的彩蛋页面，为夏日带来清凉。

## 配置要点

### 主题配置

在 `_config.anzhiyu.yml` 中进行主题相关配置：

```yaml
# AI 摘要配置
post_head_ai_description:
  enable: true
  gptName: AnZhiYu
  mode: local
  switchBtn: false
  randomNum: 3
  basicWordCount: 1000

# 菜单配置
menu:
  文章:
    隧道: /archives/ || anzhiyu-icon-box-archive
    分类: /categories/ || anzhiyu-icon-shapes
    标签: /tags/ || anzhiyu-icon-tags
  我的:
    音乐馆: /music/?id=1708664797&server=tencent || anzhiyu-icon-music
    小空调: /air-conditioner/ || anzhiyu-icon-fan
```

### 站点配置

在 `_config.yml` 中配置：

```yaml
# 搜索功能
search:
  path: search.xml
  field: post
  content: true

# 留言板
envelope_comment:
  enable: true
  path: comments
```

## 插件安装

必要的插件：

```bash
npm install -S hexo-renderer-pug hexo-renderer-sass
npm install -S hexo-butterfly-envelope
npm install -S hexo-butterfly-swiper-anzhiyu-pro
```

## 总结

通过以上配置，您就可以拥有一个功能完整、外观精美的 AnZhiYu 主题博客了。记得定期更新主题和插件以获得最新功能。

---

> 本文展示了 AnZhiYu 主题的主要配置方法，更多详细配置请参考官方文档。
