# Waline "Not initialized" 错误解决方案

## 🔴 错误原因

你遇到的 `500: Not initialized` 错误是因为 Waline 服务端没有正确配置数据库。

## ✅ 快速解决步骤

### 方案一：使用 LeanCloud 国际版（推荐）

#### 1. 注册 LeanCloud 账号
访问：https://console.leancloud.app/
- 选择**国际版**（不需要备案）
- 使用邮箱注册

#### 2. 创建应用
1. 登录后点击「创建应用」
2. 应用名称：`我的博客评论`（随意）
3. 计费方案：选择「开发版」（免费）
4. 地域：选择 `US` 或 `HK`（国际版）

#### 3. 获取密钥
进入应用后：
1. 点击左侧「设置」→「应用凭证」
2. 复制以下三个值：
   - `AppID`
   - `AppKey`
   - `MasterKey`

#### 4. 配置 Vercel 环境变量

登录 Vercel：https://vercel.com/dashboard

1. 找到你的 Waline 项目
2. 点击「Settings」→「Environment Variables」
3. 添加以下环境变量：

```
LEAN_ID = 你的AppID
LEAN_KEY = 你的AppKey  
LEAN_MASTER_KEY = 你的MasterKey
```

#### 5. 重新部署
1. 在 Vercel 项目页面
2. 点击「Deployments」标签
3. 点击最新部署右侧的三个点
4. 选择「Redeploy」

### 方案二：使用 PostgreSQL（Vercel 自带）

#### 1. 在 Vercel 创建数据库
1. 登录 Vercel Dashboard
2. 点击「Storage」
3. 选择「Create Database」
4. 选择「Postgres」
5. 给数据库命名，点击创建

#### 2. 连接数据库到项目
1. 创建完成后，点击「Connect Project」
2. 选择你的 Waline 项目
3. 点击「Connect」

#### 3. 添加环境变量
Vercel 会自动添加数据库连接变量，但你需要手动添加：

```
DATABASE_URL = 自动生成的连接字符串
JWT_TOKEN = 随机生成一个32位字符串
```

生成 JWT_TOKEN 的方法：
```bash
# 在 PowerShell 中运行
-join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | % {[char]$_})
```

#### 4. 重新部署
同方案一的步骤 5

## 🎯 验证部署

### 1. 测试 API
访问：`https://你的项目.vercel.app/api/comment`

成功返回：
```json
{
  "errno": 0,
  "errmsg": "",
  "data": {
    "page": 1,
    "totalPages": 0,
    "pageSize": 10,
    "count": 0,
    "data": []
  }
}
```

### 2. 访问管理后台
访问：`https://你的项目.vercel.app/ui`
- 第一次访问会要求注册管理员
- 使用你的邮箱注册

## 📝 博客配置

在 `_config.anzhiyu.yml` 中配置：

```yaml
# 评论系统
comments:
  use: Waline
  text: true
  lazyload: false
  count: true
  card_post_count: true

# Waline 配置
waline:
  serverURL: https://你的项目.vercel.app  # 注意：不要加斜杠结尾
  bg: /img/comment_bg.png
  pageview: true
  meta_css: true
  imageUploader: true
  option:
    lang: zh-CN
    locale:
      placeholder: '欢迎留言！支持 Markdown 语法 ～'
      sofa: '🎉 快来做第一个评论的人吧～'
      submit: '💬 发表评论'
      reply: '💬 回复'
      cancelReply: '❌ 取消回复'
      comment: '💬 条评论'
      refresh: '🔄 刷新'
      more: '📖 加载更多...'
      preview: '👀 预览'
      emoji: '😀 表情'
      uploadImage: '🖼️ 上传图片'
      admin: '博主'
    emoji:
      - https://unpkg.com/@waline/emojis@1.2.0/weibo
      - https://unpkg.com/@waline/emojis@1.2.0/qq
      - https://unpkg.com/@waline/emojis@1.2.0/bilibili
    meta: ['nick', 'mail', 'link']
    requiredMeta: ['nick', 'mail']
    wordLimit: [0, 500]
    pageSize: 10
    imageUploader: true
```

## 🛠️ 常见问题排查

### 问题1：仍然显示 Not initialized
**检查清单：**
1. ✅ 环境变量是否正确添加
2. ✅ 变量名是否完全正确（注意大小写）
3. ✅ 是否重新部署了项目
4. ✅ LeanCloud 应用是否正常运行

### 问题2：评论无法显示
**解决方法：**
1. 清除浏览器缓存
2. 检查 serverURL 是否正确（不要有多余的斜杠）
3. 查看浏览器控制台错误信息

### 问题3：CORS 跨域错误
**在 LeanCloud 设置安全域名：**
1. 进入 LeanCloud 应用
2. 设置 → 安全中心 → Web 安全域名
3. 添加：
   - `https://你的博客域名.com`
   - `https://你的项目.vercel.app`
   - `http://localhost:4000`（本地测试用）

## 🚀 快速测试

部署完成后，运行以下命令测试：

```bash
# 清理并重新生成
hexo clean

# 生成静态文件
hexo g

# 本地预览
hexo s
```

访问 http://localhost:4000 查看评论系统是否正常工作。

## 📧 配置邮件通知（可选）

如果需要评论邮件通知，在 Vercel 环境变量中添加：

```
# QQ 邮箱示例
SMTP_SERVICE = QQ
SMTP_USER = 你的QQ邮箱
SMTP_PASS = QQ邮箱授权码（不是密码）
SITE_NAME = 你的博客名称
SITE_URL = https://你的博客.com
AUTHOR_EMAIL = 你的邮箱
```

获取 QQ 邮箱授权码：
1. 登录 QQ 邮箱
2. 设置 → 账户 → POP3/SMTP 服务
3. 开启服务并生成授权码

## ⚡ 一键部署链接

如果上述方法都不行，可以尝试重新部署：

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample)

点击后直接配置环境变量即可。

## 💡 成功标志

当你看到以下情况时，说明配置成功：
1. ✅ API 测试返回正常的 JSON 数据
2. ✅ 管理后台可以正常访问
3. ✅ 博客页面显示评论框
4. ✅ 可以成功发表评论

---

如果还有问题，请提供：
1. Vercel 部署日志
2. 浏览器控制台完整错误信息
3. 你的 Waline 项目地址（vercel.app）
