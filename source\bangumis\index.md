---
title: 📺 追番页
date: 2025-08-14 14:53:19
type: bangumis
top_img: false
aside: false
comments: true
description: 记录追番历程，分享动漫心得
---

<div class="bangumi-header">

## 🎬 我的追番记录

<div class="bangumi-intro">
  <p>在这里记录我的追番历程，分享那些让人印象深刻的动漫作品 ～</p>
</div>

</div>

<div class="bangumi-stats">

### 📊 追番统计

- 🎯 **总计追番**：25+ 部
- ⭐ **五星推荐**：8 部
- 📅 **本季追番**：3 部
- 💝 **最爱类型**：治愈、悬疑、科幻

</div>

## 🔥 正在追的番

{% tabs bangumi %}
<!-- tab 2025年 -->
- 【追番中】葬送的芙莉莲 ⭐⭐⭐⭐⭐
- 【追番中】药屋少女的呢喃 ⭐⭐⭐⭐
- 【追番中】关于我转生变成史莱姆这档事 第三季 ⭐⭐⭐⭐
<!-- endtab -->

<!-- tab 2024年 -->
- 【完结】咒术回战 第二季 ⭐⭐⭐⭐⭐
- 【完结】链锯人 ⭐⭐⭐⭐
- 【完结】间谍过家家 第二季 ⭐⭐⭐⭐⭐
- 【完结】孤独摇滚！ ⭐⭐⭐⭐⭐
<!-- endtab -->

<!-- tab 2023年 -->
- 【完结】鬼灭之刃 锻刀村篇 ⭐⭐⭐⭐
- 【完结】我推的孩子 ⭐⭐⭐⭐⭐
- 【完结】天国大魔境 ⭐⭐⭐⭐
- 【完结】地狱乐 ⭐⭐⭐⭐
- 【完结】进击的巨人 最终季 完结篇 ⭐⭐⭐⭐⭐
<!-- endtab -->

<!-- tab 2022年 -->
- 【完结】间谍过家家 ⭐⭐⭐⭐⭐
- 【完结】夏日重现 ⭐⭐⭐⭐⭐
- 【完结】赛博朋克：边缘行者 ⭐⭐⭐⭐⭐
- 【完结】国王排名 ⭐⭐⭐⭐
<!-- endtab -->
{% endtabs %}

<div class="bangumi-recommendations">

## ⭐ 五星推荐

### 🏆 必看神作

{% note success modern %}
**《我推的孩子》**
- **类型**：悬疑、偶像、转生
- **推荐理由**：剧情反转不断，制作精良，探讨娱乐圈黑暗面
- **个人评分**：⭐⭐⭐⭐⭐
{% endnote %}

{% note info modern %}
**《间谍过家家》**
- **类型**：喜剧、日常、家庭
- **推荐理由**：温馨治愈，角色可爱，制作精美
- **个人评分**：⭐⭐⭐⭐⭐
{% endnote %}

{% note warning modern %}
**《赛博朋克：边缘行者》**
- **类型**：科幻、动作、悲剧
- **推荐理由**：视觉震撼，剧情深刻，音乐出色
- **个人评分**：⭐⭐⭐⭐⭐
{% endnote %}

</div>

<div class="bangumi-categories">

## 📂 分类推荐

### 🌸 治愈系
- 孤独摇滚！
- 间谍过家家
- 小林家的龙女仆

### 🔥 热血系
- 鬼灭之刃
- 咒术回战
- 进击的巨人

### 🧠 烧脑系
- 夏日重现
- 我推的孩子
- 约定的梦幻岛

### 🚀 科幻系
- 赛博朋克：边缘行者
- 攻壳机动队
- 心理测量者

</div>

<div class="bangumi-wishlist">

## 📝 想看清单

- 【待看】鬼灭之刃 柱训练篇
- 【待看】咒术回战 第三季
- 【待看】进击的巨人 真·最终季
- 【待看】间谍过家家 剧场版

</div>

<style>
.bangumi-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
  border-radius: 12px;
}

.bangumi-intro {
  font-size: 1.1rem;
  color: var(--anzhiyu-secondtext);
  margin: 1rem 0;
}

.bangumi-stats {
  background: var(--anzhiyu-card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid var(--anzhiyu-card-border);
}

.bangumi-stats h3 {
  color: var(--anzhiyu-main);
  margin-bottom: 1rem;
}

.bangumi-stats ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.bangumi-stats li {
  padding: 0.5rem;
  background: rgba(var(--anzhiyu-main-rgb), 0.05);
  border-radius: 6px;
  text-align: center;
}

.bangumi-recommendations, .bangumi-categories, .bangumi-wishlist {
  margin: 2rem 0;
}

.bangumi-recommendations h2, .bangumi-categories h2, .bangumi-wishlist h2 {
  color: var(--anzhiyu-main);
  border-left: 4px solid var(--anzhiyu-main);
  padding-left: 1rem;
  margin-bottom: 1.5rem;
}

.bangumi-categories h3 {
  color: var(--anzhiyu-fontcolor);
  margin: 1.5rem 0 0.8rem 0;
  font-size: 1.1rem;
}

.bangumi-categories ul {
  list-style: none;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.bangumi-categories li {
  background: var(--anzhiyu-card-bg);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  border: 1px solid var(--anzhiyu-card-border);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.bangumi-categories li:hover {
  background: var(--anzhiyu-main);
  color: white;
  transform: translateY(-2px);
}

.bangumi-wishlist ul {
  list-style: none;
  padding: 0;
}

.bangumi-wishlist li {
  padding: 0.8rem;
  margin: 0.5rem 0;
  background: var(--anzhiyu-card-bg);
  border-radius: 8px;
  border-left: 4px solid var(--anzhiyu-main);
  transition: all 0.3s ease;
}

.bangumi-wishlist li:hover {
  transform: translateX(5px);
  box-shadow: var(--anzhiyu-shadow-border);
}

@media (max-width: 768px) {
  .bangumi-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .bangumi-stats {
    padding: 1rem;
  }

  .bangumi-stats ul {
    grid-template-columns: 1fr;
  }

  .bangumi-categories ul {
    flex-direction: column;
  }
}
</style>
