---
title: 🎵 音乐馆
date: 2025-08-14 14:43:36
type: music
aplayer: true
top_img: false
comments: true
aside: false
description: 在这里聆听美妙的音乐，感受音符的魅力
---

<div class="music-hall-header">

## 🎶 欢迎来到音乐馆

<div class="music-intro">
  <p>音乐是心灵的语言，在这里与你分享那些触动心弦的旋律 ～</p>
</div>

</div>

<div class="music-sections">

### 🎧 精选歌单

这里收录了一些个人喜爱的音乐，涵盖不同风格和情感：

- 🌅 **清晨时光** - 适合早晨聆听的轻柔音乐
- 🌙 **夜深人静** - 深夜里的静谧之声
- 💪 **工作专注** - 提升专注力的背景音乐
- 🎉 **心情愉悦** - 让人开心的欢快旋律
- 📚 **学习伴侣** - 适合学习时的轻音乐

### 🎼 音乐推荐

#### 最近在听

- **《夜曲》** - 周杰伦
- **《稻香》** - 周杰伦
- **《青花瓷》** - 周杰伦
- **《告白气球》** - 周杰伦

#### 经典收藏

- **《月亮代表我的心》** - 邓丽君
- **《甜蜜蜜》** - 邓丽君
- **《小幸运》** - 田馥甄
- **《演员》** - 薛之谦

</div>

<div class="music-tips">

### 💡 使用说明

- 🎵 **播放控制** - 点击播放按钮开始音乐之旅
- 🔀 **随机播放** - 开启随机模式发现惊喜
- 🔁 **循环播放** - 单曲循环或列表循环
- 📱 **移动端** - 完美支持手机端播放
- 🎚️ **音量调节** - 可自由调节播放音量

</div>

<style>
.music-hall-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, rgba(var(--anzhiyu-main-rgb), 0.1), rgba(var(--anzhiyu-main-rgb), 0.05));
  border-radius: 12px;
}

.music-intro {
  font-size: 1.1rem;
  color: var(--anzhiyu-secondtext);
  margin: 1rem 0;
}

.music-sections {
  margin: 2rem 0;
}

.music-sections h3 {
  color: var(--anzhiyu-main);
  border-left: 4px solid var(--anzhiyu-main);
  padding-left: 1rem;
  margin: 2rem 0 1rem 0;
}

.music-sections h4 {
  color: var(--anzhiyu-fontcolor);
  margin: 1.5rem 0 0.8rem 0;
  font-size: 1.1rem;
}

.music-sections ul {
  list-style: none;
  padding: 0;
}

.music-sections li {
  padding: 0.5rem 0;
  border-bottom: 1px dashed var(--anzhiyu-card-border);
  transition: all 0.3s ease;
}

.music-sections li:hover {
  color: var(--anzhiyu-main);
  padding-left: 0.5rem;
}

.music-sections li:last-child {
  border-bottom: none;
}

.music-tips {
  background: var(--anzhiyu-card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid var(--anzhiyu-card-border);
}

.music-tips h3 {
  color: var(--anzhiyu-main);
  margin-bottom: 1rem;
}

.music-tips ul {
  list-style: none;
  padding: 0;
}

.music-tips li {
  padding: 0.3rem 0;
  color: var(--anzhiyu-fontcolor);
}

@media (max-width: 768px) {
  .music-hall-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .music-intro {
    font-size: 1rem;
  }

  .music-tips {
    padding: 1rem;
  }
}
</style>
