﻿## <a name="header"></a><a name="content"></a><a name="主题安装与使用hexo-框架与-butterfly安知鱼主题"></a>1. 主题安装与使用（Hexo 框架与 Butterfly/安知鱼主题）
**准备 Hexo 环境：**搭建博客前，需要安装 Node.js 和 Hexo CLI。确保本地已安装 Node.js（可从 Node 官网下载）和 Git，然后使用 npm install -g hexo-cli 安装 Hexo 命令行工具[\[1\]](https://blog.anheyu.com/posts/ddae.html#:~:text=node)[\[2\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%AE%89%E8%A3%85%20Git)。接着，在目标文件夹执行 hexo init 初始化博客，安装依赖后，即生成一个基础的 Hexo 博客框架。若您已完成 Hexo 初始搭建，可直接进入主题安装。

**下载并安装安知鱼主题：**安知鱼主题是基于 Hexo 的 Butterfly 主题魔改而来[\[3\]](https://docs.anheyu.com/intro#:~:text=%E9%A2%84%E8%A7%88%3A%20AnZhiYu%20)。在 Hexo 博客根目录下的 themes/ 文件夹中安装主题，有三种方式：\
\- **方式一（推荐，GitHub）：**克隆 GitHub 仓库的稳定版主分支。命令：git clone -b main https://github.com/anzhiyu-c/hexo-theme-anzhiyu.git themes/anzhiyu[\[4\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%80)。如果网速不佳，可使用代理链接[\[5\]](https://docs.anheyu.com/initall#:~:text=bash)。\
\- **方式二（Release 压缩包）：**从主题仓库下载最新发布的 release 压缩包[\[6\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%BA%8C)，解压到 themes 目录并重命名文件夹为“anzhiyu”。\
\- **方式三（NPM 安装）：**确保 Hexo 版本 ≥ 5.0，执行 npm i hexo-theme-anzhiyu 安装[\[7\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%89)。此方式主题文件会安装到 node\_modules 中（无需复制主题文件夹）。

**应用主题：**主题安装后，打开博客根目录下的主配置文件 \_config.yml，找到 theme: 设置，将其值改为 anzhiyu，保存即可启用安知鱼主题[\[8\]](https://docs.anheyu.com/initall#:~:text=%E6%89%93%E5%BC%80%20Hexo%20%E6%A0%B9%E7%9B%AE%E5%BD%95%E4%B8%8B%E7%9A%84%20,anzhiyu)。例如：

theme: anzhiyu

接着，还需安装主题依赖的渲染引擎插件。在博客根目录执行：

npm install hexo-renderer-pug hexo-renderer-stylus --save

安装 Pug 模板引擎与 Stylus 样式引擎，以确保主题的 .pug 和 .styl 文件可以正确渲染[\[9\]](https://docs.anheyu.com/initall#:~:text=%E5%AE%89%E8%A3%85%20pug%20%E5%92%8C%20stylus%20%E6%B8%B2%E6%9F%93%E6%8F%92%E4%BB%B6)。如果直连安装缓慢，可使用国内源地址重试[\[10\]](https://docs.anheyu.com/initall#:~:text=%E6%97%A0%E6%B3%95%E5%AE%89%E8%A3%85%E5%8F%AF%E4%BB%A5%E4%BD%BF%E7%94%A8cnpm%E8%BF%9B%E8%A1%8C%E5%AE%89%E8%A3%85)。

**主题配置优先级与覆盖：**安知鱼主题提供**覆盖配置**机制，将主题配置文件复制到站点根目录，实现配置与主题解耦。具体做法：复制主题目录下的 \_config.yml 文件到博客根目录，并重命名为 \_config.anzhiyu.yml[\[11\]](https://docs.anheyu.com/initall#:~:text=bash)。之后修改任何主题相关配置，都在根目录的 \_config.anzhiyu.yml 中进行。需要注意：若存在 \_config.anzhiyu.yml，其中设置的参数优先级高于站点 \_config.yml，修改原 \_config.yml 中相应项将无效[\[12\]](https://docs.anheyu.com/initall#:~:text=%E6%B3%A8%E6%84%8F%EF%BC%9A)。每次主题升级后应关注 release 说明，对比新旧配置差异并同步更新 \_config.anzhiyu.yml[\[13\]](https://docs.anheyu.com/initall#:~:text=,debug%60%20%E6%9F%A5%E7%9C%8B%E5%91%BD%E4%BB%A4%E8%A1%8C%E8%BE%93%E5%87%BA%E3%80%82)。使用覆盖配置可避免主题更新时自定义配置被覆盖。

**运行与预览：**在本地启动 Hexo 服务器查看效果。在博客目录执行：

hexo clean  # 清除缓存\
hexo generate  # 生成静态文件\
hexo server  # 启动本地服务器

然后访问 http://localhost:4000 即可在浏览器中预览博客[\[14\]](https://docs.anheyu.com/initall#:~:text=%E6%9C%AC%E5%9C%B0%E5%90%AF%E5%8A%A8%20hexo)[\[15\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E5%8D%B3%E5%8F%AF%E7%99%BB%E5%BD%95%20http%3A%2F%2Flocalhost%3A4000%2F%20%E6%9F%A5%E7%9C%8B%E6%95%88%E6%9E%9C)。首次启用主题建议多刷新几次，若有样式异常可能是缓存问题。另外，每次执行 hexo s 或 hexo g 前最好先 hexo clean 清理缓存，以避免旧文件干扰[\[16\]](https://docs.anheyu.com/initall#:~:text=,%E7%9B%AE%E5%BD%95%EF%BC%9B)。

**主题升级：**当主题发布新版本时，可按照原安装方式更新。若通过 GitHub 或 release 压缩包安装，无代码改动的情况下，可以直接删除旧的 themes/anzhiyu 文件夹或重命名备份，然后重新 clone 或解压最新版本[\[17\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%80)[\[18\]](https://docs.anheyu.com/initall#:~:text=1)。升级后对比新的 \_config.yml 与原先的 \_config.anzhiyu.yml，同步添加新配置项或修改变更项[\[19\]](https://docs.anheyu.com/initall#:~:text=)。使用 npm 安装的用户，则可在博客目录执行 npm update --save hexo-theme-anzhiyu 更新[\[20\]](https://docs.anheyu.com/initall#:~:text=%E5%9C%A8%E5%8D%9A%E5%AE%A2%E7%9B%AE%E5%BD%95%E4%B8%8B%E6%89%A7%E8%A1%8C%E5%91%BD%E4%BB%A4%EF%BC%9A)。更新完成后同样检查配置变更。

💡 **提示：** 安知鱼主题基于 Butterfly 主题开发[\[3\]](https://docs.anheyu.com/intro#:~:text=%E9%A2%84%E8%A7%88%3A%20AnZhiYu%20)。在使用前应了解 Hexo 的基本用法，例如新建分类和标签的方法等[\[21\]](https://docs.anheyu.com/intro#:~:text=%E5%A6%82%E6%9E%9C%E8%83%BD%E7%BB%99%E6%88%91%E4%B8%80%E4%B8%AA%20star%20%E9%82%A3%E5%B0%86%E6%98%AF%E5%AF%B9%E6%88%91%E8%8E%AB%E5%A4%A7%E7%9A%84%E9%BC%93%E5%8A%B1%E3%80%82%E4%BD%BF%E7%94%A8%E8%BF%99%E4%B8%AA%E4%B8%BB%E9%A2%98%E4%B9%8B%E5%89%8D%EF%BC%8C%E4%BD%A0%E5%BA%94%E8%AF%A5%E6%98%8E%E7%99%BD%E5%AE%83%E6%98%AF%E4%B8%80%E4%B8%AAHexo%E4%B8%BB%E9%A2%98%EF%BC%8C%E5%AE%83%E7%9A%84%E5%9F%BA%E6%9C%AC%E9%80%BB%E8%BE%91%E7%A6%BB%E4%B8%8D%E5%BC%80Hexo%EF%BC%8C%E5%85%B3%E4%BA%8E%E5%A6%82%E4%BD%95%E6%96%B0%E5%BB%BA%E5%88%86%E7%B1%BB%EF%BC%8C%E5%A6%82%E4%BD%95%E6%96%B0%E5%BB%BA%E6%A0%87%E7%AD%BE%E8%BF%99%E4%BA%9B%20%E9%97%AE%E9%A2%98%E5%BA%94%E8%AF%A5%E5%9C%A8%E4%BD%BF%E7%94%A8%E4%B9%8B%E5%89%8D%E5%B0%B1%E4%BB%8E%E4%BA%92%E8%81%94%E7%BD%91%E6%88%96%E5%AE%98%E6%96%B9%E6%96%87%E6%A1%A3%E4%BA%86%E8%A7%A3%E8%AF%A6%E6%83%85%E3%80%82)。若在使用主题过程中遇到问题，可先查阅本指南和配置注释；本指南未涵盖的 Hexo 通用问题请参考 Hexo 官方文档。遇到主题 bug 可在主题仓库提交 issue[\[22\]](https://docs.anheyu.com/initall#:~:text=%E5%8F%A6%E5%A4%96%E6%9C%AC%E6%8C%87%E5%8D%97%E4%BB%85%E5%8C%85%E5%90%AB%E4%B8%BB%E9%A2%98%E8%8C%83%E5%9B%B4%E5%86%85%E7%9A%84%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%98%AF%20Hexo%20%E7%9A%84%E4%BD%BF%E7%94%A8%E6%88%96%E8%80%85%20Hexo%20%E6%8F%92%E4%BB%B6%E7%9A%84%E4%BD%BF%E7%94%A8%EF%BC%8C%E8%AF%B7%E6%9F%A5%E9%98%85%E5%90%84%E8%87%AA%E7%9A%84%E6%96%87%E6%A1%A3%E3%80%82)。
## <a name="博客部署本地预览github-pages-和-vercel-等"></a>2. 博客部署（本地预览、GitHub Pages 和 Vercel 等）
**本地部署预览：**利用 Hexo 内置服务器进行本地预览调试。执行 hexo server 后，可在浏览器访问 http://localhost:4000 查看博客[\[15\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E5%8D%B3%E5%8F%AF%E7%99%BB%E5%BD%95%20http%3A%2F%2Flocalhost%3A4000%2F%20%E6%9F%A5%E7%9C%8B%E6%95%88%E6%9E%9C)。本地看到的效果即是生成的静态页面，调整配置或撰写文章后都可以通过重新生成并刷新页面验证效果。注意 Hexo 默认不开启 404 页面本地跳转，如需测试 404 页面，可直接访问 http://localhost:4000/404.html 查看[\[23\]](https://docs.anheyu.com/page/404#:~:text=404%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE)。

**部署到 GitHub Pages：**GitHub Pages 提供免费托管静态网站的服务，非常适合部署 Hexo 博客[\[24\]](https://blog.anheyu.com/posts/ddae.html#:~:text=Github%20Pages%20%E5%8F%AF%E4%BB%A5%E8%A2%AB%E8%AE%A4%E4%B8%BA%E6%98%AF%E7%94%A8%E6%88%B7%E7%BC%96%E5%86%99%E7%9A%84%E3%80%81%E6%89%98%E7%AE%A1%E5%9C%A8%20github%20%E4%B8%8A%E7%9A%84%E9%9D%99%E6%80%81%E7%BD%91%E9%A1%B5%E3%80%82%E4%BD%BF%E7%94%A8,Github%20Pages%20%E5%8F%AF%E4%BB%A5%E4%B8%BA%E4%BD%A0%E6%8F%90%E4%BE%9B%E4%B8%80%E4%B8%AA%E5%85%8D%E8%B4%B9%E7%9A%84%E6%9C%8D%E5%8A%A1%E5%99%A8%EF%BC%8C%E5%85%8D%E5%8E%BB%E4%BA%86%E8%87%AA%E5%B7%B1%E6%90%AD%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%99%A8%E5%92%8C%E5%86%99%E6%95%B0%E6%8D%AE%E5%BA%93%E7%9A%84%E9%BA%BB%E7%83%A6%E3%80%82%E6%AD%A4%E5%A4%96%E8%BF%98%E5%8F%AF%E4%BB%A5%E7%BB%91%E5%AE%9A%E8%87%AA%E5%B7%B1%E7%9A%84%E5%9F%9F%E5%90%8D%E3%80%82)。简要部署步骤：

1. **创建仓库：**在 GitHub 上新建一个仓库，命名为 yourusername.github.io（将 yourusername 替换为你的 GitHub 用户名）[\[25\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%B8%80%E3%80%81%E7%99%BB%E5%BD%95%20Github%20%E6%89%93%E5%BC%80%E8%87%AA%E5%B7%B1%E7%9A%84%E9%A1%B9%E7%9B%AE%20yourname)。这是 GitHub Pages 约定的个人站点地址。确保仓库设置为公开。
1. **配置 Hexo deploy：**编辑博客根目录下的 \_config.yml，在底部找到或添加 deploy: 配置段，指定部署方式为 git。例如：

   deploy:\
   `  `type: git\
   `  `repo: **************:yourusername/yourusername.github.io.git\
   `  `branch: main

   其中 repo 填写刚创建的仓库的 SSH 地址（或 HTTPS 地址）[\[26\]](https://blog.anheyu.com/posts/ddae.html#:~:text=)[\[27\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%BA%94%E3%80%81%E6%BB%91%E5%88%B0%E6%9C%80%E4%B8%8B%E9%9D%A2%2C%E6%8C%89%E4%B8%8B%E5%9B%BE%E4%BF%AE%E6%94%B9%20_config)。若使用 SSH，需要先在本地生成 SSH Key 并添加到 GitHub 帐号（GitHub Settings -> SSH and GPG keys）以授权免密推送[\[28\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%B8%89%E3%80%81%E9%85%8D%E7%BD%AE%20,QiQi_Blog%60%EF%BC%89%20%60Git%20Bash%20Here%60%20%E8%BE%93%E5%85%A5%E4%BB%A5%E4%B8%8B%E5%91%BD%E4%BB%A4)[\[29\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%9B%9B%E3%80%81%E5%9C%A8%20GitHub%20%E8%B4%A6%E6%88%B7%E4%B8%AD%E6%B7%BB%E5%8A%A0%E4%BD%A0%E7%9A%84%E5%85%AC%E9%92%A5)。
1. **安装部署插件：**Hexo 默认不包含 git 部署，需要安装插件。执行命令安装 hexo-deployer-git：

   npm install hexo-deployer-git --save

   安装成功后再运行部署命令[\[30\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E4%B9%8B%E5%90%8E%E4%BC%9A%E8%AE%A9%E4%BD%A0%E8%BE%93%E5%85%A5%E4%BD%A0%E7%9A%84%20Github%20%E7%9A%84%E8%B4%A6%E5%8F%B7%E5%92%8C%E5%AF%86%E7%A0%81%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%AD%A4%E6%97%B6%E6%8A%A5%E4%BB%A5%E4%B8%8B%E9%94%99%E8%AF%AF%EF%BC%8C%E8%AF%B4%E6%98%8E%E4%BD%A0%E7%9A%84%20deployer%20%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E6%88%90%E5%8A%9F)。如果执行 hexo g -d 时出现 “ERROR Deployer not found: git”，说明插件未安装成功或未正确配置，需要重新安装上述插件[\[30\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E4%B9%8B%E5%90%8E%E4%BC%9A%E8%AE%A9%E4%BD%A0%E8%BE%93%E5%85%A5%E4%BD%A0%E7%9A%84%20Github%20%E7%9A%84%E8%B4%A6%E5%8F%B7%E5%92%8C%E5%AF%86%E7%A0%81%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%AD%A4%E6%97%B6%E6%8A%A5%E4%BB%A5%E4%B8%8B%E9%94%99%E8%AF%AF%EF%BC%8C%E8%AF%B4%E6%98%8E%E4%BD%A0%E7%9A%84%20deployer%20%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E6%88%90%E5%8A%9F)。
1. **生成并部署：**一切就绪后，运行：

   hexo clean && hexo generate  \
   hexo deploy  

   或简写为 hexo g -d，Hexo 将生成静态文件并自动将public/目录推送到配置的仓库分支[\[31\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%85%AD%E3%80%81%E5%9C%A8%20Hexo%20%E6%96%87%E4%BB%B6%E5%A4%B9%E4%B8%8B%E5%88%86%E5%88%AB%E6%89%A7%E8%A1%8C%E4%BB%A5%E4%B8%8B%E5%91%BD%E4%BB%A4)。首次部署可能会要求输入 GitHub 用户名和密码（或 token）。完成后，GitHub Pages 仓库将出现生成的文件。几秒钟后即可通过 https://yourusername.github.io 访问博客[\[32\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%BD%A0%E7%9A%84%E5%8D%9A%E5%AE%A2%E5%B0%B1%E4%BC%9A%E9%83%A8%E7%BD%B2%E5%88%B0%20Github%20%E4%B8%8A%E4%BA%86)。
1. **自定义域名（可选）：**如果有自定义域名，可在博客 source/ 目录下放置一个名为 CNAME 的文件，内容写入域名（不要包含 http://），并在域名DNS解析中添加一条CNAME记录指向 yourusername.github.io。GitHub Pages 检测到 CNAME 文件后，会为该域提供服务。

**部署到 Vercel 等平台：**Vercel 和 Netlify 等前端云平台也非常适合托管 Hexo 博客。其原理通常是连接您的 Git 仓库，在每次推送时自动构建并部署网站。以 **Vercel** 为例：首先将整个 Hexo 博客源码（含 source、themes 等）上传到一个Git仓库（推荐使用私人仓库保存源码），然后在 Vercel 仪表盘中新建项目，导入该仓库并选择 Hexo 项目。Vercel 会检测到 Hexo 并自动安装依赖、执行构建命令（默认 hexo generate）并部署输出。部署成功后，可绑定自定义域名。安知鱼博主本人的博客采取了多平台部署策略：**在 GitHub Pages、Vercel、Netlify 同时部署**，其中 Vercel 和 Netlify 会自动从 GitHub 仓库拉取更新，实现多线路加速和容灾[\[33\]](https://blog.anheyu.com/posts/b228.html#:~:text=)[\[34\]](https://blog.anheyu.com/posts/b228.html#:~:text=%E5%B7%A5%E4%BD%9C%E6%B5%81)。这种方式需要结合 GitHub Actions 等 CI 工具，自动将博客源码推送到 Pages 仓库，从而触发 Vercel/Netlify 的更新[\[34\]](https://blog.anheyu.com/posts/b228.html#:~:text=%E5%B7%A5%E4%BD%9C%E6%B5%81)。一般用户如果希望简便，可以选择单一平台部署。相比之下：

- **GitHub Pages：**免费稳定，但国内访问速度稍慢，可结合 Cloudflare 加速。
- **Vercel：**对国内较友好（但不保证所有地区），构建和全球分发速度快，支持自定义域名和HTTPS，配置简单。
- **Netlify：**类似 Vercel，也提供全球CDN加速和自动部署。

根据需求选择其一即可。如需多平台同时部署，可通过 GitHub Actions 配置一键部署到多个远端。安知鱼在 CI 脚本中实现了一次文章提交同时部署到 GitHub Pages（作为主站）和 Vercel/Netlify 等镜像站的流程，提高了可用性和访问速度[\[33\]](https://blog.anheyu.com/posts/b228.html#:~:text=)。

**其他部署方式：**除了上述平台，Hexo 生成的静态文件也可部署到任何静态托管，如阿里云OSS+CDN、腾讯云COS、又或自行购买服务器/Nginx 托管。原理都是将 public/ 文件上传至网页根目录。Hexo官方提供多种部署插件，如支持将文件发布到 FTP、GitLab Pages 等。配置方法与 GitHub Pages 类似，在 \_config.yml 的 deploy 中填写相应信息，然后执行 hexo deploy 上传。总之，Hexo 博客一旦生成静态文件，部署非常灵活。

💡 **本地与远程调试：**建议在本地通过 hexo s 仔细检查页面显示，无误后再部署到线上。若部署后访问异常，大多是缓存或域名配置问题，可清理浏览器缓存或等待一段时间再查看[\[35\]](https://docs.anheyu.com/initall#:~:text=,%E6%B8%85%E9%99%A4%E6%9C%AC%E5%9C%B0%E7%BC%93%E5%AD%98%EF%BC%9B)。另外，如果使用 CDN 或多点部署，更新后可能出现新文章无法立即看到，这是缓存延迟正常现象。
## <a name="站点配置_config.yml-与主题配置"></a>3. 站点配置（\_config.yml 与主题配置）
Hexo 博客有两份重要配置文件：**站点配置文件**（Hexo 根目录下的 \_config.yml）和 **主题配置文件**（主题目录下的 \_config.yml 或复制到根目录的 \_config.anzhiyu.yml）[\[36\]](https://docs.anheyu.com/initall#:~:text=)。两者分别控制整体站点设置和主题外观功能设置，务必区分。

**基本站点信息：**打开站点 \_config.yml 可设置博客的基本信息，例如：\
\- title: 网站主标题，会显示在站点首页的标题栏等位置。\
\- subtitle: 副标题，可以是一句格言或座右铭。\
\- description: 网站描述，**若使用 PJAX 建议务必填写**，用于确保页面切换时元信息正确[\[37\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=)。\
\- author: 博主昵称。\
\- language: 默认语言，Hexo 默认 en，可改为 zh-CN 简体中文等[\[38\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E9%BB%98%E8%AE%A4%E8%AF%AD%E8%A8%80%E6%98%AF%20en)。\
\- url: 博客的网址（部署后访问的主域名），例如 https://yourdomain.com。\
\- timezone: 时区，不设则默认使用系统时间。

填写正确的站点信息有助于 SEO 和页面展示。例如，安知鱼主题支持多语言，可在 \_config.yml 将 language 改为 zh-CN 切换为中文界面[\[39\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=)。

**主题配置概述：**主题配置文件 \_config.anzhiyu.yml 包含大量选项，用于定制博客外观和功能。下面将分模块介绍常用配置项：

- **导航菜单（menu）：**控制顶部菜单的栏目及链接。默认配置提供了“文章”（含隧道=归档、分类、标签），“友链”（友人帐、朋友圈、留言板），“我的”（音乐馆、追番页、相册集、小空调），“关于”（关于本人、闲言碎语、随便逛逛）等菜单结构[\[40\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=menu%3A%20%E6%96%87%E7%AB%A0%3A%20%E9%9A%A7%E9%81%93%3A%20%2Farchives%2F%20,tags)[\[41\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E9%9F%B3%E4%B9%90%E9%A6%86%3A%20%2Fmusic%2F%20%7C%7C%20anzhiyu,fan)。格式形如：

  menu:\
  `  `文章:\
  `    `隧道: /archives/ || anzhiyu-icon-box-archive\
  `    `分类: /categories/ || anzhiyu-icon-shapes\
  `    `标签: /tags/ || anzhiyu-icon-tags\
  `  `友链:\
  `    `友人帐: /link/ || anzhiyu-icon-link\
  `    `朋友圈: /fcircle/ || anzhiyu-icon-artstation\
  `    `留言板: /comments/ || anzhiyu-icon-envelope\
  `  `我的:\
  `    `音乐馆: /music/ || anzhiyu-icon-music\
  `    `追番页: /bangumis/ || anzhiyu-icon-bilibili\
  `    `相册集: /album/ || anzhiyu-icon-images\
  `    `小空调: /air-conditioner/ || anzhiyu-icon-fan\
  `  `关于:\
  `    `关于本人: /about/ || anzhiyu-icon-paper-plane\
  `    `闲言碎语: /essay/ || anzhiyu-icon-lightbulb\
  `    `随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1

  配置规则：菜单名对应下级若干子项，每项格式为“名称: 链接地址 || 图标类名”。如果不想显示图标，可省略 “|| 图标” 部分[\[42\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E5%BF%85%E9%A1%BB%E6%98%AF%20%60%2Fxxx%2F%60%EF%BC%8C%E5%90%8E%E9%9D%A2%60)。菜单文字可自行修改为喜欢的词语，但链接应指向对应页面路径。**注意：**如果不需要某些菜单，将对应行加上注释 # 即可隐藏[\[43\]](https://docs.anheyu.com/page/links#:~:text=menu%3A%20,tags)[\[44\]](https://docs.anheyu.com/page/links#:~:text=%E5%85%B3%E4%BA%8E%3A%20,prints1)。
- **站点外观风格：**主题提供多种外观设置，如：
- **主色调**：主题可以根据文章头图自动变化主色调，也可指定固定颜色。cover\_change: true 表示文章页主色随封面图变化[\[45\]](https://docs.anheyu.com/advanced/#:~:text=%23%20%E9%A1%B9%E7%9B%AE%E5%9C%B0%E5%9D%80%EF%BC%9Ahttps%3A%2F%2Fgithub.com%2Fanzhiyu,%E6%95%B4%E7%AF%87%E6%96%87%E7%AB%A0%E8%B7%9F%E9%9A%8Fcover%E4%BF%AE%E6%94%B9%E4%B8%BB%E8%89%B2%E8%B0%83)。若要全站统一色调，可关闭此功能并在样式中指定主色。
- **深色模式**：可配置夜间模式按钮。设置 darkmode.enable: true 则启用深色模式支持，darkmode.button: true 则在页面右下角显示日/夜间切换按钮[\[46\]](https://docs.anheyu.com/global/extra#:~:text=,6%20pm%20to%206%20am)。另外 autoChangeMode 可设置自动切换模式，例如 1 表示跟随系统（系统不支持暗色则在每天 18:00-6:00 间启用暗色），2 表示总是根据时间段自动切换[\[47\]](https://docs.anheyu.com/global/extra#:~:text=button%3A%20true%20,value%20is%206%20and%2018)[\[48\]](https://docs.anheyu.com/global/extra#:~:text=%E5%8F%82%E6%95%B0%20%E8%A7%A3%E9%87%8A%20button%20%E6%98%AF%E5%90%A6%E5%9C%A8%E5%8F%B3%E4%B8%8B%E8%A7%92%E6%98%BE%E7%A4%BA%E6%97%A5%E5%A4%9C%E6%A8%A1%E5%BC%8F%E5%88%87%E6%8D%A2%E6%8C%89%E9%92%AE%20autoChangeMode,dark%20mode%20autoChangeMode%3A%20false%20%E5%8F%96%E6%B6%88%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2)。可以根据需要调整自动切换的开始 (start) 和结束 (end) 时间。
- **代码高亮样式**：Hexo 集成 Prismjs 和 Highlight.js 等，多数主题允许配置代码配色方案。安知鱼主题内置 6 种代码高亮主题供选择：darker、pale night、light、ocean、mac、mac light[\[49\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=)。可以在主题配置中设置，如：highlight\_theme: light 切换为浅色背景代码主题[\[50\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E4%BF%AE%E6%94%B9%20)。
- **站点功能开关：**主题配置中还有许多开关项，用于启用/禁用特定功能。例如：
- **文章字数统计**：Hexo 可通过插件统计字数和阅读时长。若启用主题的字数统计（在主题配置中开启相应选项，如 wordcount），需要安装 hexo-wordcount 插件，否则会出现 “wordcount is not a function” 错误[\[51\]](https://docs.anheyu.com/faq#:~:text=wordcount%20is%20not%20a%20function)[\[52\]](https://docs.anheyu.com/faq#:~:text=bash)。解决方法是进入博客目录，执行 npm install hexo-wordcount --save 安装插件[\[53\]](https://docs.anheyu.com/faq#:~:text=%E4%BA%A7%E7%94%9F%E5%8E%9F%E5%9B%A0%EF%BC%9A%E5%BC%80%E5%90%AF%E4%BA%86wordcount%E7%9A%84%E5%AD%97%E6%95%B0%E7%BB%9F%E8%AE%A1%EF%BC%8C%E4%BD%86%E6%98%AF%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E5%AF%B9%E5%BA%94%E6%8F%92%E4%BB%B6%E3%80%82)。
- **访客统计**：主题内置不蒜子 (Busuanzi) 访问统计功能，可显示站点 UV、PV 及每篇文章阅读量。在主题配置中找到 busuanzi 段，设置 site\_uv, site\_pv, page\_pv 为 true 即可开启相应计数[\[54\]](https://docs.anheyu.com/global/extra#:~:text=busuanzi%3A%20site_uv%3A%20true%20site_pv%3A%20true,page_pv%3A%20true)。开启后页面底部或文章信息处会显示访问量数字。若需要修改 Busuanzi 的加载源，可在 CDN 设置中调整[\[55\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L342%20,%E7%9A%84%20CDN%20%E4%B8%AD%E7%9A%84%20option%20%E8%BF%9B%E8%A1%8C%E4%BF%AE%E6%94%B9)。
- **站点运行时间**：可在页脚显示网站运行天数，配置项通常在 footer.runtime 中。启用后需要设置站点上线时间 launch\_time，主题会自动计算并展示运行时长[\[56\]](https://docs.anheyu.com/global/extra#:~:text=owner%3A%20enable%3A%20true%20since%3A%202020,%E4%B8%8A%E7%8F%AD%E6%91%B8%E9%B1%BC%E4%B8%AD.svg)[\[57\]](https://docs.anheyu.com/global/extra#:~:text=offduty_description%3A%20%E4%B8%8B%E7%8F%AD%E4%BA%86%E5%B0%B1%E8%AF%A5%E5%BC%80%E5%BC%80%E5%BF%83%E5%BF%83%E7%9A%84%E7%8E%A9%E8%80%8D%EF%BC%8C%E5%98%BF%E5%98%BF~%20,%E5%BE%BD%E6%A0%87%E6%8F%90%E7%A4%BA%E8%AF%AD)。
- **公告/欢迎语**：可以设置网站加载时的欢迎消息或顶部公告栏，安知鱼主题支持自定义**欢迎语**（如进入站点时页面问候语）。可在配置中找到 message 或相关字段进行设置。还有“控制台彩蛋信息”等，都可按需开启[\[58\]](https://docs.anheyu.com/advanced/#:~:text=,%E9%A6%96%E9%A1%B5%E9%A1%B6%E9%83%A8%203%20%E5%A4%A7%E5%88%86%E7%B1%BB%E9%85%8D%E7%BD%AE)[\[59\]](https://docs.anheyu.com/advanced/#:~:text=,%E5%8A%A8%E6%95%88%E6%8E%A7%E5%88%B6)。
- **资源与性能**：站点配置还涉及资源加载优化，比如**PJAX 无刷新加载**、**PWA 离线支持**、**CDN 设置**等：
- **PJAX**：主题默认开启 PJAX，它通过 AJAX 局部刷新页面提升速度[\[60\]](https://docs.anheyu.com/global/extra#:~:text=%E4%BD%BF%E7%94%A8pjax%E5%90%8E%EF%BC%8C%E4%B8%80%E4%BA%9B%E4%B8%AA%E5%88%AB%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E7%9A%84js%2Fcss%EF%BC%8C%E5%B0%86%E4%BC%9A%E6%94%B9%E4%B8%BA%E6%89%80%E6%9C%89%E9%A1%B5%E9%9D%A2%E9%83%BD%E5%8A%A0%E8%BD%BD)。PJAX 对很多内置功能做了适配，建议保持开启。如需关闭，将配置项 pjax.enable 改为 false，但需注意可能造成部分功能失效[\[60\]](https://docs.anheyu.com/global/extra#:~:text=%E4%BD%BF%E7%94%A8pjax%E5%90%8E%EF%BC%8C%E4%B8%80%E4%BA%9B%E4%B8%AA%E5%88%AB%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E7%9A%84js%2Fcss%EF%BC%8C%E5%B0%86%E4%BC%9A%E6%94%B9%E4%B8%BA%E6%89%80%E6%9C%89%E9%A1%B5%E9%9D%A2%E9%83%BD%E5%8A%A0%E8%BD%BD)。
- **PWA**：如需离线访问和缓存，可为博客添加渐进式网页应用支持。安知鱼主题提供了两种方案：安装 hexo-offline 插件（适合初学者）或 hexo-swpp 插件（进阶，自定义 Service Worker）[\[61\]](https://docs.anheyu.com/global/extra#:~:text=PWA)。以 hexo-offline 为例，安装插件后在根目录创建 hexo-offline.config.cjs 按指南配置缓存规则，然后重新部署即可[\[62\]](https://docs.anheyu.com/global/extra#:~:text=1,offline.config.cjs%60%20%E6%96%87%E4%BB%B6%EF%BC%8C%E5%B9%B6%E5%A2%9E%E5%8A%A0%E4%BB%A5%E4%B8%8B%E5%86%85%E5%AE%B9%E3%80%82)[\[63\]](https://docs.anheyu.com/global/extra#:~:text=js)。配置正确的话，博客可在断网时加载缓存页面。
- **CDN 加速**：主题 \_config.yml 底部提供了 CDN 配置，可以替换主题内部和第三方资源的加载链接[\[64\]](https://docs.anheyu.com/global/extra#:~:text=CDN)[\[65\]](https://docs.anheyu.com/global/extra#:~:text=,internal_provider%3A%20cbd)。默认使用作者提供的 npm.elemecdn 镜像加速。如需自行部署静态资源CDN，可将对应脚本样式的链接替换为自定义地址[\[65\]](https://docs.anheyu.com/global/extra#:~:text=,internal_provider%3A%20cbd)[\[66\]](https://docs.anheyu.com/global/extra#:~:text=,https%3A%2F%2Fnpm.elemecdn.com%2F%24%7Bname%7D%40latest%2F%24%7Bfile)。不过**非必要不建议修改**，除非您有可靠的自建 CDN，以免链接失效导致资源加载失败。

以上只是站点与主题配置的一部分。安知鱼主题配置项非常丰富，每个配置在文件中基本都有注释解释。建议读者结合实际需求通读 \_config.anzhiyu.yml，按注释提示调整。如有配置不清楚含义，可查阅官方文档对应章节或搜索相关关键词以获取更多说明。
## <a name="页面配置关于页友链页归档页标签页分类页等"></a>4. 页面配置（关于页、友链页、归档页、标签页、分类页等）
Hexo 博客除了一般的文章页面外，还可以生成多种**特殊页面**，如“关于我”页面、友情链接页、归档页、分类索引页、标签云页等。这些页面通常通过 Hexo 的 hexo new page 命令创建，并配合主题的特殊布局或配置来呈现。下面分别介绍常见页面的配置方法：

- **归档页（Archives）：**归档页用于汇总展示所有文章列表，Hexo 默认会根据配置自动生成归档页面（通常访问路径为 /archives/）。如果在菜单中启用了 “隧道” 或 “归档” 链接（指向 /archives/），无需额外配置即可使用。某些主题可能要求手动创建归档页，但安知鱼主题基于 Butterfly，一般 Hexo 自带归档生成器已启用。因此确保站点配置 \_config.yml 中有 archive\_generator 配置（Hexo 默认开启）即可。在导航菜单中已有 archives 链接的情况下，访问 /archives/ 会列出文章归档。如果归档页未生效，可以尝试手动创建：运行 hexo new page archives，然后在生成的 source/archives/index.md 中添加 type: "archives" 字段以调用主题的归档布局（Butterfly 主题可能不需要这一步）。安知鱼主题还有选项控制归档页是否显示文章封面缩略图，可通过 archives\_enable 开关设置[\[67\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=archives_enable%3A%20true%20,right%20%2C%20both%20position%3A%20both)。
- **分类页（Categories）：**用于按分类汇总文章。Hexo 默认需新建分类页。步骤：执行命令 hexo new page categories，会生成文件 source/categories/index.md[\[68\]](https://docs.anheyu.com/page/classify#:~:text=1)[\[69\]](https://docs.anheyu.com/page/classify#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。打开该文件，在 front-matter（顶部信息块）中添加：

  title: 分类\
  type: "categories"

  安知鱼主题要求 type: "categories" 才会套用主题内置的分类页模板[\[70\]](https://docs.anheyu.com/page/classify#:~:text=4.%20%E4%BF%AE%E6%94%B9%E8%BF%99%E4%B8%AA%E6%96%87%E4%BB%B6%EF%BC%9A%20%E8%AE%B0%E5%BE%97%E6%B7%BB%E5%8A%A0%20%60type%3A%20)。可选地设置 aside: false（是否显示侧边栏）和 top\_img: false（是否显示顶部大图）等参数[\[71\]](https://docs.anheyu.com/page/classify#:~:text=,)。保存后重新生成，访问 /categories/ 即可看到按分类列出的文章索引。分类页面一般会按分类名字母顺序显示，每个分类名下标注文章数量。
- **标签页（Tags）：**用于按标签汇总文章。创建方法类似分类页：运行 hexo new page tags，生成 source/tags/index.md[\[72\]](https://docs.anheyu.com/page/tags#:~:text=1)[\[73\]](https://docs.anheyu.com/page/tags#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。在该文件 front-matter 中添加 type: "tags"[\[73\]](https://docs.anheyu.com/page/tags#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。同样可设置 comments: false 禁用评论，top\_img: false 禁用顶部图等[\[74\]](https://docs.anheyu.com/page/tags#:~:text=,comments%3A%20false%20top_img%3A%20false)。启用后访问 /tags/ 会显示所有标签及文章数。主题支持按名称、文章数等对标签排序，可通过配置 orderby 和 order 来控制（如按名称或数量排序，正序或倒序）[\[75\]](https://docs.anheyu.com/page/tags#:~:text=%E5%8F%82%E6%95%B0%20%E8%A7%A3%E9%87%8A%20type%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%EF%BC%8C%E5%BF%85%E9%A1%BB%E4%B8%BA%20tags%20comments%E3%80%96%E5%8F%AF%E9%80%89%E3%80%97%E6%98%AF%E5%90%A6%E6%98%BE%E7%A4%BA%E8%AF%84%E8%AE%BA,1%2C%20desc%20for%20descending)。
- **友链页（友情链接）：**展示博主的友情链接列表。在安知鱼主题下需进行以下配置：
- **新建页面：**执行 hexo new page link，将在 source/link/index.md 创建友链页面文件[\[76\]](https://docs.anheyu.com/page/links#:~:text=1)[\[77\]](https://docs.anheyu.com/page/links#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。打开该文件，设置 front-matter：title: 友人帐（标题可自定），以及 **必须添加** type: "link" 字段[\[77\]](https://docs.anheyu.com/page/links#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。这会告知主题将此页面作为友链页处理。
- **准备友链数据：**在博客 source/\_data/ 目录下新建一个文件 link.yml（如果没有 \_data 文件夹请先创建）[\[78\]](https://docs.anheyu.com/page/links#:~:text=5.%20%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%20%60source,link.yml%60%EF%BC%8C%E8%BE%93%E5%85%A5%EF%BC%9A)。按照主题要求的格式填写友链列表数据。例如[\[79\]](https://docs.anheyu.com/page/links#:~:text=yml)[\[80\]](https://docs.anheyu.com/page/links#:~:text=,static%401.0.4%2Fimg%2Favatar.jpg%20descr%3A%20%E7%94%9F%E6%B4%BB%E6%98%8E%E6%9C%97%EF%BC%8C%E4%B8%87%E7%89%A9%E5%8F%AF%E7%88%B1)：

  - class\_name: 框架\
  `  `flink\_style: flexcard\
  `  `hundredSuffix: ""\
  `  `link\_list:\
  `    `- name: Hexo\
  `      `link: https://hexo.io/zh-tw/\
  `      `avatar: https://d33wubrfki0l68.cloudfront.net/6657ba.../logo.svg\
  `      `descr: 快速、简单且强大的静态网站框架\
  `    `- name: anzhiyu主题\
  `      `link: https://blog.anheyu.com/\
  `      `avatar: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.4/img/avatar.jpg\
  `      `descr: 生活明朗，万物可爱\
  `      `siteshot: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.6/img/blog.anheyu.com.jpg\
      - class\_name: 推荐博客\
  `  `flink\_style: telescopic\
  `  `hundredSuffix: ""\
  `  `link\_list:\
  `    `- name: 安知鱼\
  `      `link: https://blog.anheyu.com/\
  `      `avatar: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.4/img/avatar.jpg\
  `      `descr: 生活明朗，万物可爱\
  `      `siteshot: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.6/img/blog.anheyu.com.jpg\
  `      `color: vip\
  `      `tag: 技术

  上述 YAML 中，按照 “分类” 列出友链：class\_name 是分类名称，flink\_style 是展示风格（内置 flexcard、telescopic、anzhiyu 三种风格可选[\[81\]](https://docs.anheyu.com/page/links#:~:text=class_name%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E5%90%8D%20class_desc%E3%80%96%E5%8F%AF%E9%80%89%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E6%8F%8F%E8%BF%B0%20flink_style%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97,link_list%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%97%E8%A1%A8%20link_list.name%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%90%8D%E7%A7%B0%20link_list.link%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E9%93%BE%E6%8E%A5%20link_list.avatar%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%A4%B4%E5%83%8F)），link\_list 列出该分类下的各个友链条目，包括名称、链接、头像 URL、描述等[\[81\]](https://docs.anheyu.com/page/links#:~:text=class_name%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E5%90%8D%20class_desc%E3%80%96%E5%8F%AF%E9%80%89%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E6%8F%8F%E8%BF%B0%20flink_style%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97,link_list%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%97%E8%A1%A8%20link_list.name%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%90%8D%E7%A7%B0%20link_list.link%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E9%93%BE%E6%8E%A5%20link_list.avatar%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%A4%B4%E5%83%8F)（可选参数如 siteshot 网站截图，color 和 tag 可用于标记推荐等[\[82\]](https://docs.anheyu.com/page/links#:~:text=link_list,%EF%BC%8C%E6%8F%90%E4%BE%9B%E4%BA%86%E4%B8%A4%E4%B8%AA%E5%BF%AB%E6%8D%B7%E9%A2%9C%E8%89%B2%E9%80%89%E9%A1%B9%E5%88%86%E5%88%AB%E6%98%AF%20%60vip%20%60%E5%92%8C%20%60speed)）。可以根据自己需要增删分类或友链条目。
- **菜单开启：**在主题配置文件中，将导航菜单里与友链相关的项取消注释。例如默认 menu 配置里，“友链”菜单下的“友人帐”对应 /link/ 已经给出，但前面有 # 需要去掉[\[83\]](https://docs.anheyu.com/page/links#:~:text=menu%3A%20,tags)。注意缩进保持正确。启用后顶部导航才会出现友情链接入口。
- **高级配置（可选）：**安知鱼主题提供“与数百博主共同进步”模块，即友链页面顶部的大标题和提交友链的说明文字。在主题配置 \_config.anzhiyu.yml 里找到并启用 linkPageTop 设置，enable: true 则开启该模块，并可以自定义显示的标题和提示[\[84\]](https://docs.anheyu.com/page/links#:~:text=%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E4%B8%AD%20)（如示例中标题是“与数百名博主无限进步”[\[85\]](https://docs.anheyu.com/page/links#:~:text=,n)）。这一模块适合友链很多的情况下使用，可引导别人提交友链。

完成以上步骤后，重新生成博客，即可在 /link/ 页面看到美观的友情链接列表。若觉得默认样式不满足要求，可以通过修改 link.yml 中的 flink\_style 切换布局，或自定义 CSS 调整样式。

- **关于页（About）：**用于介绍博主个人信息。“关于”页在安知鱼主题中有比较丰富的定制内容，包括头像、座右铭、技能清单、人生经历、统计信息等，需要结合数据文件配置：
- **创建页面：**执行 hexo new page about，生成 source/about/index.md[\[86\]](https://docs.anheyu.com/page/about#:~:text=1)[\[87\]](https://docs.anheyu.com/page/about#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。打开文件，设置 front-matter：

  title: 关于\
  type: "about"\
  aside: false\
  top\_img: false\
  background: "#f8f9fe"\
  comments: false

  这里最重要的是 type: "about"[\[87\]](https://docs.anheyu.com/page/about#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)告知主题使用关于页模板。其它如 aside: false 表示隐藏侧边栏，top\_img: false 不使用顶部大图，background 可以设置页面背景色为淡色（默认 about 页一般用浅色背景），comments: false 关闭评论（可选）。
- **菜单入口：**在主题配置的菜单中，取消注释关于页链接。例如默认菜单的“关于”分类下有“关于本人: /about/ || icon-zhifeiji”，确保这一行生效，使顶部导航出现“关于”入口[\[88\]](https://docs.anheyu.com/page/about#:~:text=%E5%85%B3%E4%BA%8E%3A%20%E5%85%B3%E4%BA%8E%E6%9C%AC%E4%BA%BA%3A%20%2Fabout%2F%20%7C%7C%20icon,prints1)。同时可以隐藏或修改“闲言碎语”等其他子项。
- **填写关于页内容：**安知鱼主题将关于页的具体内容以数据文件方式配置。在 source/\_data/ 下新建 about.yml，按照主题文档提供的格式填写个人信息[\[89\]](https://docs.anheyu.com/page/about#:~:text=%E6%96%B0%E5%BB%BA%20)[\[90\]](https://docs.anheyu.com/page/about#:~:text=,aboutsiteTips%3A%20tips%3A%20%E8%BF%BD%E6%B1%82%20title1%3A%20%E6%BA%90%E4%BA%8E)。例如：

  - class\_name: 关于页\
  `  `subtitle: 生活明朗，万物可爱✨\
  `  `avatarImg: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.0/img/avatar.webp\
  `  `avatarSkills:\
  `    `left:\
  `      `- 数码科技爱好者\
  `      `- 分享与热心帮助\
  `      `- 智能家居小能手\
  `      `- 设计开发一条龙\
  `    `right:\
  `      `- 专修交互与设计\
  `      `- 脚踏实地行动派\
  `      `- 团队小组发动机\
  `      `- 壮汉人狠话不多\
  `  `name: 陈志伟\
  `  `description: 是一名前端工程师、学生、独立开发者、博主\
  `  `aboutsiteTips:\
  `    `tips: 追求\
  `    `title1: 源于\
  `    `title2: 热爱而去 感受\
  `    `word:\
  `      `- 学习\
  `      `- 生活\
  `      `- 程序\
  `      `- 体验\
  `  `helloAbout: Hello there!\
  `  `skillsTips:\
  `    `tips: 技能\
  `    `title: 开启创造力\
  `  `careers:\
  `    `tips: 生涯\
  `    `title: 无限进步\
  `    `list:\
  `      `- desc: EDU，软件工程专业\
  `        `color: "#357ef5"\
  `      `- desc: EDU，软件工程专业\
  `        `color: "#357ef5"\
    ...（下略）

  以上仅截取部分示例，可见 about.yml 可以配置**昵称** (name)，**个人描述** (description)，**头像** (avatarImg)，**座右铭** (subtitle)，**技能列举** (avatarSkills 列表中左右栏各列出几点)，**人生/职业经历** (careers 列表可以写教育背景、工作经历等，每项有颜色表示)，**自定义问候语** (helloAbout)，**页面小标题** (tips 和 title 类似用于不同板块)，**文章统计** (statistic 可以设置一个链接和文字用于跳转归档，比如文章总数)等等[\[91\]](https://docs.anheyu.com/page/about#:~:text=,%E6%99%BA%E8%83%BD%E5%AE%B6%E5%B1%85%E5%B0%8F%E8%83%BD%E6%89%8B)[\[90\]](https://docs.anheyu.com/page/about#:~:text=,aboutsiteTips%3A%20tips%3A%20%E8%BF%BD%E6%B1%82%20title1%3A%20%E6%BA%90%E4%BA%8E)。这些字段对应主题 about 页面上不同板块的内容，填写时可根据实际情况增删。默认示例相当详实，读者可参考并做适当精简。
- 保存 about.yml 后，重新生成博客并访问 /about/，即可看到精心设计的关于我页面。若需调整样式或者部分内容不需要，可删除相应 YAML 字段，主题会做判断。另外，如果不想使用主题提供的复杂关于页布局，也可以直接在 about/index.md 编写 Markdown 内容，这种情况下不要设置 type: "about"，而用普通页面形式显示。
- **「随笔/杂谈」页（闲言碎语）：**默认菜单中“关于”下有“闲言碎语”链接（/essay/）。这是用来发表日常随笔或短内容的页面。安知鱼主题对此也有专门模板，可选择性使用。如果想启用，需 hexo new page essay 创建页面，并在 front-matter 设置 type: "essay"（假如主题有定义该类型）。由于提问中未具体要求，此处不展开说明。
- **其它内置页面：**安知鱼主题还提供了一些特别页面配置，在文档“进阶配置”部分有详述，如：
- **“我的设备”页**（展示博主常用设备列表）[\[92\]](https://docs.anheyu.com/advanced/#:~:text=%E7%95%99%E8%A8%80%E6%9D%BF%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE)。
- **“追番”页**（展示追看的番剧列表）[\[93\]](https://docs.anheyu.com/advanced/#:~:text=%E6%88%91%E7%9A%84%E8%A3%85%E5%A4%87%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE)。
- **“音乐馆”页**（音乐播放列表页面）[\[94\]](https://docs.anheyu.com/advanced/#:~:text=%E5%85%B3%E4%BA%8E%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE)。
- **“朋友圈”页**（集成友链朋友圈动态）等[\[95\]](https://docs.anheyu.com/advanced/#:~:text=%E9%9F%B3%E4%B9%90%E9%A6%86%E9%A1%B5%E9%85%8D%E7%BD%AE)。

这些页面一般需要结合特定的数据源或插件使用，比如朋友圈页面需要配置后端 API 地址等[\[95\]](https://docs.anheyu.com/advanced/#:~:text=%E9%9F%B3%E4%B9%90%E9%A6%86%E9%A1%B5%E9%85%8D%E7%BD%AE)。如果有需要，可参考官方文档对应章节继续配置。

**小结：**利用 Hexo 的 pages 功能和主题自定义模板，我们可以轻松拓展出丰富的独立页面。创建页面时牢记两点：一是通过 hexo new page <name> 新建 Markdown 页面文件，二是在该文件的 Front-matter 中设置正确的 type（如果主题要求）[\[73\]](https://docs.anheyu.com/page/tags#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)[\[69\]](https://docs.anheyu.com/page/classify#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20)。此外，别忘了在导航菜单里加入这些页面的链接，确保访客能方便地从主菜单进入页面[\[83\]](https://docs.anheyu.com/page/links#:~:text=menu%3A%20,tags)[\[88\]](https://docs.anheyu.com/page/about#:~:text=%E5%85%B3%E4%BA%8E%3A%20%E5%85%B3%E4%BA%8E%E6%9C%AC%E4%BA%BA%3A%20%2Fabout%2F%20%7C%7C%20icon,prints1)。通过以上方法，您的博客将不仅有文章列表，还有完善的关于页和友链等，让整体更加丰富专业。
## <a name="插件配置与扩展seo样式美化统计暗色模式加载动画等"></a>5. 插件配置与扩展（SEO、样式美化、统计、暗色模式、加载动画等）
Hexo 拥有大量插件和主题配置，可以为博客添加各种功能和美化效果。安知鱼主题本身集成了许多特性，只需启用相应配置即可，无需额外安装插件。但对于某些 Hexo 通用功能，可能需要额外安装插件。以下从几个方面介绍扩展配置：

**SEO 优化：**良好的 SEO 能使博客在搜索引擎中有更高可见度。Hexo 本身提供了基本的 SEO 标签支持，如站点 \_config.yml 中的 title、description 等。安知鱼主题进一步支持**Open Graph** 协议，在页面 <head> 中加入 Open Graph meta 标签，包括文章缩略图、标题、发布时间等[\[96\]](https://docs.anheyu.com/global/extra#:~:text=%E5%9C%A8%20,Open%20Graph%20%E7%9A%84%E5%86%85%E5%AE%B9%EF%BC%8C%E5%B1%95%E7%A4%BA%E7%BC%A9%E7%95%A5%E5%9B%BE%EF%BC%8C%E6%A0%87%E9%A2%98%E7%AD%89%E7%AD%89%E4%BF%A1%E6%81%AF%E3%80%82)。默认情况下主题已开启 Open Graph：在主题配置里有 Open\_Graph\_meta.enable: true[\[97\]](https://docs.anheyu.com/global/extra#:~:text=,com%2Fdocs%2Fsharing%2Fwebmasters%2F%20Open_Graph_meta%3A%20enable%3A%20true)。建议保持开启，这样当您将文章链接分享至社交平台时，会显示预览卡片（含标题、描述、封面图等）。另外主题配置里还有针对 Twitter 卡片、Google+、Facebook App ID 等选项[\[98\]](https://docs.anheyu.com/global/extra#:~:text=enable%3A%20true%20option%3A%20,fb_app_id)，如有需要可以填写。除了 Open Graph，还应确保每篇文章的 Front-matter 里设置了合理的 description （若未设置，主题会截取部分正文作为描述）。这样搜索引擎或分享链接才能获取正确的摘要信息。

**样式美化：**安知鱼主题注重美观，默认样式已经相当优秀。当然您可以进一步个性化：\
\- **自定义配色和字体：**可以通过修改主题配置中的颜色变量或引入自定义 CSS 实现。例如更换站点的主字体或调整链接颜色，都可在 \_config.anzhiyu.yml 里的 css 或 theme\_color 等选项中调整。如果需要插入自定义 CSS/JS，主题提供了 inject 接口，可在配置里将代码片段加入到 <head> 或页面底部[\[99\]](https://docs.anheyu.com/global/extra#:~:text=Inject)[\[100\]](https://docs.anheyu.com/global/extra#:~:text=inject%3A%20head%3A%20,%3Cscript%20src%3D%22xxxx%22%3E%3C%2Fscript)。比如：

inject:\
`  `head:\
`    `- <link rel="stylesheet" href="/css/my-style.css">\
`  `bottom:\
`    `- <script src="/js/my-script.js"></script>

这样可以在不修改主题源码的情况下添加额外样式脚本。

- **段落排版优化（中英混排空格）：**中文和英文混杂书写时，经常需要在文字和英文/数字之间增加空格以美观。主题内置了 **Pangu.js** 支持，只需在配置中启用即可自动插入空格。配置项：

  pangu:\
  `  `enable: true\
  `  `field: site

  将 enable 设为 true，则会对整站内容应用 Pangu 空格优化（field: site 表示全站，设为 post 则仅对文章部分生效）[\[101\]](https://docs.anheyu.com/global/extra#:~:text=yml)[\[102\]](https://docs.anheyu.com/global/extra#:~:text=3%204%205)。启用后，无需手动在 Markdown 中添加空格，页面展示将自动调整中英文间距，更加舒适。
- **图片灯箱与懒加载：**安知鱼主题支持图片**大图查看模式**，即点击文章中的图片会弹出灯箱预览。默认使用 [fancybox] 和 [medium-zoom] 两种方案[\[103\]](https://docs.anheyu.com/global/extra#:~:text=fancybox%20medium_zoom)。配置里确保：

  fancybox: true\
  medium\_zoom: true

  即可启用这两个插件[\[104\]](https://docs.anheyu.com/global/extra#:~:text=)。这样当用户点击文章图片时，会以灯箱方式全屏显示，提供更佳的观看体验。同时，主题对文章图片默认开启懒加载（仅当图片出现在视口时才加载），这在提高页面加载性能的同时，也避免一次性加载过多图片影响速度。
- **暗色模式（夜间模式）：**前面站点配置部分已提到，安知鱼主题支持深色模式切换。通过配置 darkmode 下的选项，可以决定是否显示夜间模式开关按钮，以及是否自动根据时间或系统设置切换[\[46\]](https://docs.anheyu.com/global/extra#:~:text=,6%20pm%20to%206%20am)[\[105\]](https://docs.anheyu.com/global/extra#:~:text=autoChangeMode%20%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2%E7%9A%84%E6%A8%A1%E5%BC%8F%20autoChangeMode%20autoChangeMode%3A%201,dark%20mode%20autoChangeMode%3A%20false%20%E5%8F%96%E6%B6%88%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2)。大多数情况下建议开启此功能，让用户可以自行选择浅色或深色阅读。深色模式下主题还提供一些酷炫的小效果，例如页面背景的星空粒子效果等（可在配置中寻找相关开关，如 darkmode.star 或类似的选项，开启后夜晚模式背景会出现动态星空粒子[\[106\]](https://docs.anheyu.com/advanced/#:~:text=,%E6%B7%B1%E8%89%B2%E6%A8%A1%E5%BC%8F%E7%B2%92%E5%AD%90%E6%95%88%E6%9E%9Ccanvas)）。
- **页面加载动画：**为了改善加载体验，主题支持**进度条/加载动画**。开启后，用户在等待页面加载时会看到转圈动画或进度指示，而非空白页面。安知鱼主题内置对 pace.js 的支持[\[107\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L732%20%E4%B8%BB%E9%A2%98%E6%94%AF%E6%8C%81%20pace,js)。配置方法是在主题配置中找到 preloader 部分：

  preloader:\
  `  `enable: true\
  `  `source: 3\
  `  `# 1: 全屏Loading动画，2: 进度条(Pace)，3: 同时启用以上所有动画\
  `  `avatar: # 可选，自定义加载动画中的头像图片URL

  将 enable 设为 true 即开启[\[108\]](https://docs.anheyu.com/global/extra#:~:text=,io%2Fpace)。source 可以选择动画类型，默认值 3 表示全屏动画+顶部进度条全启用[\[108\]](https://docs.anheyu.com/global/extra#:~:text=,io%2Fpace)（可根据喜好调整）。另有 avatar 选项，可以设置一个图片，使其在加载时作为动画元素（比如显示站长头像等）[\[109\]](https://docs.anheyu.com/global/extra#:~:text=%E4%B8%BB%E9%A2%98%E6%94%AF%E6%8C%81%20pace)。启用加载动画后，当进入页面时如果加载较慢，将先显示动画，直到页面内容加载完毕再呈现，有效避免页面内容“突兀”加载问题[\[110\]](https://docs.anheyu.com/global/extra#:~:text=%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E5%8A%A8%E7%94%BB)[\[111\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L738%20,source)。

**访问统计与分析：**除了不蒜子访客计数，可能还需更专业的统计分析工具。安知鱼主题集成了多种站点分析支持，配置简单：\
\- **百度统计：**注册百度统计并获取一段 JS 跟踪代码，将其粘贴在主题配置的 baidu\_analytics 字段即可[\[112\]](https://docs.anheyu.com/global/extra#:~:text=%E7%99%BE%E5%BA%A6%E7%BB%9F%E8%AE%A1)[\[113\]](https://docs.anheyu.com/global/extra#:~:text=baidu_analytics%3A%20%E4%BD%A0%E7%9A%84%E4%BB%A3%E7%A0%81)。主题会自动将代码插入页面。\
\- **Google Analytics：**获取 GA 的 Tracking ID（一般格式为 UA-XXXXX-X），填入 google\_analytics 配置项[\[114\]](https://docs.anheyu.com/global/extra#:~:text=1)[\[115\]](https://docs.anheyu.com/global/extra#:~:text=google_analytics%3A%20%E4%BD%A0%E7%9A%84%E4%BB%A3%E7%A0%81%20%23%20%E9%80%9A%E5%B8%B8%E4%BB%A5%60UA)。新版 GA4 如果提供的是测量ID（G-开头），主题可能也兼容，可直接填入。\
\- **Cloudflare Web Analytics：**Cloudflare 提供无侵入的网页分析，可登录 Cloudflare 后获取一个 JS 片段，其中包含一个 token。将 token 填入主题配置的 cloudflare\_analytics: 下[\[116\]](https://docs.anheyu.com/global/extra#:~:text=Cloudflare)[\[117\]](https://docs.anheyu.com/global/extra#:~:text=%23%20Cloudflare%20Analytics%20%23%20https%3A%2F%2Fwww.cloudflare.com%2Fzh,cloudflare_analytics)。\
\- **Microsoft Clarity：**这是微软的用户行为分析工具。获取 Clarity 的 project ID，填入 microsoft\_clarity 配置项[\[118\]](https://docs.anheyu.com/global/extra#:~:text=Microsoft%20Clarity)[\[119\]](https://docs.anheyu.com/global/extra#:~:text=4.%20%E4%BF%AE%E6%94%B9%20)。

启用上述任一分析后，重新部署博客即可开始统计访客流量和行为。注意：不要开启多个重复功能的分析服务，以免相互干扰或影响页面加载速度。一般选择一两个主要的即可。

**广告和插入脚本：**如果希望在博客上挂广告（如 Google AdSense），主题也提供了方便的集成选项。例如开启 Google AdSense 自动广告：在配置中找到 google\_adsense，将 enable 设为 true，并填入您的客户端 ID（以 ca-pub-xxxxxxxx 开头的代码）到 client 字段[\[120\]](https://docs.anheyu.com/global/extra#:~:text=%E8%B0%B7%E6%AD%8C%E5%B9%BF%E5%91%8A)[\[121\]](https://docs.anheyu.com/global/extra#:~:text=google_adsense%3A%20enable%3A%20true%20auto_ads%3A%20true,%E5%A1%AB%E5%85%A5%E4%B8%AA%E4%BA%BA%E8%AF%86%E5%88%AB%E7%A0%81%20enable_page_level_ads%3A%20true)。主题会自动加载谷歌广告脚本并开启自动广告位。另外，通过配置 Inject 或直接修改模板，您也可以放置站内的其他广告或脚本，例如添加站长统计、聊天客服等第三方插件代码，都可以通过类似方式注入。

**样式和前端优化其他选项：**安知鱼主题的配置中还有许多针对页面细节和性能的开关：\
\- **CSS 浏览器前缀**：某些 CSS 属性需要加浏览器厂商前缀才能兼容老旧浏览器。开启 css\_prefix: true 后，会自动为部分 CSS 增加前缀[\[122\]](https://docs.anheyu.com/global/extra#:~:text=CSS%20%E5%89%8D%E7%BC%80)[\[123\]](https://docs.anheyu.com/global/extra#:~:text=,to%20ensure%20compatibility%20css_prefix%3A%20true)（代价是略微增大样式文件体积）。现代浏览器环境可选择不开启，保持默认即可。\
\- **页面快照分享**：配置 snapshot\_enable 等选项，可在每篇文章提供截图/生成长图等功能（视主题支持情况，安知鱼主题功能列表中未明确，但部分魔改主题有类似功能）。\
\- **Snackbar 通知**：主题提供 Snackbar 弹窗通知组件，可以用于弹出简短提示。如开启留言成功提示等。在配置里 snackbar.enable 设为 true，并设置显示位置（如 bottom-right）[\[124\]](https://docs.anheyu.com/global/extra#:~:text=,left%20bg_light%3A%20%27%2349b1f5%27%20%23light%20mode%E6%97%B6%E5%BC%B9%E7%AA%97%E8%83%8C%E6%99%AF)[\[125\]](https://docs.anheyu.com/global/extra#:~:text=bottom,dark%20mode%E6%97%B6%E5%BC%B9%E7%AA%97%E8%83%8C%E6%99%AF)。当有事件触发时会在屏幕角落弹出提示条。

总之，插件与扩展的配置重在**按需开启**：不需要的功能尽量关闭，以减少资源加载。需要的功能根据文档说明正确配置并安装相应插件。安知鱼主题的大部分扩展功能都已内置，只需在 \_config.anzhiyu.yml 中设置即可生效，这也是它的便利之处[\[126\]](https://docs.anheyu.com/intro#:~:text=%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7)（例如多种评论系统、字数统计、暗色模式等都已集成，无需繁琐对接）。在配置过程中可以多利用 Hexo 的调试命令（如 hexo g --debug）查看配置是否被正确读取[\[12\]](https://docs.anheyu.com/initall#:~:text=%E6%B3%A8%E6%84%8F%EF%BC%9A)。
## <a name="评论系统配置如-walinetwikoogiscus-等"></a>6. 评论系统配置（如 Waline、Twikoo、Giscus 等）
一个互动的评论系统能让博客更具活力。安知鱼主题内置支持**多种评论系统**，包括无后端的 Valine/Twikoo、带后端的 Waline、基于 GitHub 的 Artalk 和（新版中）**Giscus** 等。您可以在主题配置中选择一种或两种评论系统并进行配置。主题允许同时启用两套评论（会在前台提供选项卡切换），但一般推荐选用一种。下面介绍常用评论系统的配置：

- **Twikoo：**这是一款**无后端**评论系统，数据存储利用腾讯云开发环境，比较适合不想自己搭建服务器的用户。配置步骤：首先根据 Twikoo 官方文档在腾讯云开通环境并部署 Twikoo 服务，然后获得环境ID（envId）。在主题配置中，找到 comments 配置，将 use 设置为 Twikoo（如果只用一种评论就填一个）[\[127\]](https://docs.anheyu.com/initall#:~:text=,%E7%89%88%E6%9C%AC%E5%8F%B7%E9%87%8A%E4%B9%89)[\[128\]](https://docs.anheyu.com/advanced/#:~:text=,comment%20count%20in%20post%27s%20top_img)。接着在主题配置的 twikoo 部分填入刚才的 envId[\[129\]](https://docs.anheyu.com/advanced/#:~:text=Twikoo)[\[130\]](https://docs.anheyu.com/advanced/#:~:text=,envId%3A%20region%3A%20visitor%3A%20false%20option)：

  comments:\
  `  `use: Twikoo\
  twikoo:\
  `  `envId: your-env-id  # 输入你的环境ID\
  `  `region: ap-shanghai # 环境地域，不是上海则需要改，如北京改为 ap-beijing\
  `  `visitor: false  # 是否启用阅读量统计（启用则Twikoo会记录文章阅读次数）

  填好后部署博客。这样，评论框会加载 Twikoo，访客留言将存储在您的腾讯云环境中。若需要进一步配置（如表情包、自定义公告等），可参考 Twikoo 官方文档[\[131\]](https://docs.anheyu.com/advanced/#:~:text=)。安知鱼主题还贴心得提供了**匿名评论邮箱**等功能开关，如果想允许匿名访客评论，也可以在配置中开启相应选项（例如 comment\_anonymous 等）。
- **Waline：**从 Valine 衍生的**后端式**评论系统，需要您搭建后端（可部署在 LeanCloud、Vercel 等）。Waline 的好处是功能更丰富（头像挂件、点赞、浏览量统计等）并支持 Markdown。配置 Waline 前，需要部署 Waline 服务端并获得 ServerURL。在主题配置中，将 comments.use 包含 Waline[\[132\]](https://docs.anheyu.com/advanced/#:~:text=,load%20when%20comment%20element%20enters)。然后在 waline 配置节下填入：

  waline:\
  `  `serverURL: https://your-waline-vercel.app  # 填你的 Waline 部署地址\
  `  `bg: ''        # （可选）Waline 评论框背景，可设颜色或图片\
  `  `pageview: true  # 是否启用浏览量统计

  如上，将 serverURL 指向你的 Waline 服务地址[\[133\]](https://docs.anheyu.com/advanced/#:~:text=match%20at%20L280%20waline%3A%20serverURL%3A,waline%20background%20pageview%3A%20false)。设置 pageview: true 后，文章页的浏览次数会由 Waline 后端提供，而不再使用不蒜子[\[134\]](https://docs.anheyu.com/advanced/#:~:text=bg%3A%20,false)。配置保存并部署后，Waline 评论就会出现在文章底部。Waline 还支持更多自定义，可在 Waline 服务端配置管理员昵称、表情包、上传图片功能等。
- **Giscus：**这是基于 **GitHub Discussions** 的评论系统，无需自建服务器，利用 GitHub 讨论区存储评论，访客需要用 GitHub 账号登录留言。安知鱼主题在2024年中增加了对 Giscus 的内置支持。使用前，您需要在 GitHub 上创建一个讨论（Discussion）类型的仓库（通常可以直接用博客的源码仓库或专门新建一个 repo），并在 Giscus 官网生成配置代码，其中会得到仓库名、仓库ID、讨论分类ID等参数。然后在主题配置中进行如下设置：

  comments:\
  `  `use: Giscus\
  giscus:\
  `  `repo: yourusername/your-repo      # GitHub 仓库名 "用户名/仓库名"\
  `  `repo\_id: "MDEwOlJlcG9zaXRvcnkxMj..."   # 仓库的 ID （一长串字符串）\
  `  `category\_id: "DIC\_kwDO..."        # Giscus 对应的讨论分类 ID\
  `  `theme:\
  `    `light: light   # 浅色模式主题\
  `    `dark: dark     # 深色模式主题\
  `  `option:\
  `    `data-lang: zh-CN        # 界面语言中文\
  `    `data-mapping: pathname  # 评论映射到讨论的方式\
  `    `data-category: General  # （可选）Discussion 分类名称\
  `    `data-input-position: top  # 评论框位置（输入框在上方）

  以上参数中，repo 填写 GitHub 仓库路径，repo\_id 和 category\_id 可从 Giscus 提供的代码片段中找到[\[135\]](https://tunglamc.github.io/20240607123500/#:~:text=giscus%3A%20repo%3A%20,CN)。theme 可指定 Giscus 小部件的样式主题（light/dark 等，也可选 transparent\_dark 等特殊主题）。option 下可以配置 Giscus 的其他设置，比如界面语言、评论排序等。填写后部署博客。由于 Giscus 依赖 GitHub OAuth，首次加载时访客需点击“登录 GitHub”授权，然后即可发表评论。Giscus 的好处是评论内容就是 GitHub 上的讨论，天然支持 Markdown、表情，而且对开发者来说管理方便。如果主题启用了 Giscus，建议在仓库的 Discussions 设置中添加一个默认的 giscus 分类，并可以新建一个空白讨论用来放评论（不过按照 data-mapping 配置，通常 Giscus会自动以文章路径作为 discussion 标题进行匹配，无需手动为每篇文章创建讨论）。
- **Valine：**如果有人喜欢老牌的 Valine（基于 LeanCloud 存储，无后端），也可用。配置和 Waline 类似，在 LeanCloud 创建应用拿到 appId 和 appKey，然后在主题配置里添加：

  comments:\
  `  `use: Valine\
  valine:\
  `  `appId: your-leancloud-appid\
  `  `appKey: your-leancloud-appkey\
  `  `avatar: mp  # 默认头像风格（"mp"表示随机萌娃）\
  `  `visitor: false  # 是否记录阅读量

  保存后部署即可[\[136\]](https://docs.anheyu.com/advanced/#:~:text=yml)[\[137\]](https://docs.anheyu.com/advanced/#:~:text=valine%3A%20appId%3A%20,valine%20background%20visitor%3A%20false)。不过需要注意 Valine 项目本身维护不太积极，建议新博客用户考虑使用 Twikoo 或 Waline 等替代方案。
- **其他评论系统：**安知鱼主题还内置了 Artalk（自托管的评论系统，类似 Valine）、Utterances（GitHub issue 评论）等支持。如果您有其他偏好，可以查阅主题文档“评论”章节了解配置方法[\[138\]](https://docs.anheyu.com/advanced/#:~:text=yml)。大体来说，都遵循“启用对应名称 -> 填写必要 ID/Key”等信息即可接入。

配置好评论系统后，重新生成部署，访问文章页面即可看到评论区域出现。在安知鱼主题中，如果 comments.use 配置了两个服务，例如 Twikoo,Waline[\[132\]](https://docs.anheyu.com/advanced/#:~:text=,load%20when%20comment%20element%20enters), 页面会同时加载两套评论并提供切换按钮（第一个作为默认显示）[\[139\]](https://docs.anheyu.com/advanced/#:~:text=%E5%BC%80%E5%90%AF%E8%AF%84%E8%AE%BA%E9%9C%80%E8%A6%81%E5%9C%A8%20comments)。评论框上方通常会有评论系统名称标签，可以点选切换。如果只配置一个，则直接显示该评论。您可以根据自己的需要选择最适合的评论系统：**无需后端**的 Twikoo/Valine 部署简单，**需要后端**的 Waline 可扩展性强，**基于 GitHub** 的 Giscus 对技术社区用户友好等等。

最后提醒：如果开启评论**阅读量统计**（如 Waline 的 pageview，Twikoo 的 visitor，Valine 的 visitor），请注意避免与不蒜子重复。建议只启用一种浏览量统计，以免数字不一致。安知鱼主题会在检测到您开启评论自带的统计时，自动停用不蒜子的计数以免冲突[\[134\]](https://docs.anheyu.com/advanced/#:~:text=bg%3A%20,false)。
## <a name="自定义功能看板娘音乐播放器打字效果代码高亮字体图标等"></a>7. 自定义功能（看板娘、音乐播放器、打字效果、代码高亮、字体图标等）
这一节介绍如何为博客添加一些个性化的定制功能，很多属于“锦上添花”效果，包括网站看板娘（萌妹子形象助手）、背景音乐播放器、打字机动画效果、自定义图标等。安知鱼主题在这方面也提供了一些内置支持或扩展途径。

- **Live2D 看板娘：**看板娘是指网页上漂浮的卡通人物形象，通常能跟随鼠标互动或展示提示语。安知鱼主题本身未集成看板娘插件，但我们可以借助 Hexo 插件 **hexo-helper-live2d** 来实现。配置步骤：首先在博客根目录执行：

  npm install --save hexo-helper-live2d

  安装 Live2D 插件。然后在站点配置文件 \_config.yml 中添加关于看板娘的设置[\[140\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%E7%84%B6%E5%90%8E%E5%9C%A8%20hexo%20%E7%9A%84%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20,%E6%96%87%E6%A1%A3%EF%BC%9A)[\[141\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=model%3A%20use%3A%20live2d,300%20mobile%3A%20show%3A%20true%20react)：

  live2d:\
  `  `enable: true\
  `  `scriptFrom: local   # 使用本地模式加载\
  `  `pluginRootPath: live2dw/   # 插件资源根目录\
  `  `pluginJsPath: lib/         # JS文件路径（相对上述目录）\
  `  `pluginModelPath: assets/   # 模型文件路径\
  `  `tagMode: false     # 是否使用only某个页面模式\
  `  `debug: false\
  `  `model:\
  `    `use: live2d-widget-model-wanko  # 模型包名称，可安装其他模型\
  `  `display:\
  `    `position: right   # 看板娘显示在页面右下角\
  `    `width: 150        # 模型宽度\
  `    `height: 300       # 模型高度\
  `  `mobile:\
  `    `show: true        # 移动端上是否显示\
  `  `react:\
  `    `opacity: 0.7      # 默认透明度（鼠标悬停时会提升）

  上述配置参考了 hexo-helper-live2d 插件文档和示例[\[142\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=live2d%3A%20enable%3A%20true%20scriptFrom%3A%20local,tagMode%3A%20false%20debug%3A%20false%20model)[\[143\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=display%3A%20position%3A%20right%20width%3A%20150,7)。简单解释：scriptFrom: local 表示使用本地模型资源（更稳定，需安装模型包），model.use 指定所用模型的名称（例子用了 live2d-widget-model-wanko，即一只小狗模型），插件默认提供一些模型，可以通过 npm 安装更多模型包（如 npm install live2d-widget-model-shizuku 等)[\[144\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%E7%84%B6%E5%90%8E%E4%B8%8B%E8%BD%BD%E6%A8%A1%E5%9E%8B%EF%BC%8C%E6%A8%A1%E5%9E%8B%E5%90%8D%E7%A7%B0%E5%8F%AF%E4%BB%A5%E5%88%B0%20%E8%BF%99%E9%87%8C%20%E5%8F%82%E8%80%83%EF%BC%8C%E4%B8%80%E4%BA%9B%E6%A8%A1%E5%9E%8B%E7%9A%84%E9%A2%84%E8%A7%88%E5%8F%AF%E4%BB%A5%E5%9C%A8%20%E8%BF%99%E9%87%8C%E3%80%82)。设置好后，重新启动 Hexo，本地预览即可看到页面右下角出现可爱的看板娘形象。您可以与之互动，看板娘通常会有随机的提示语和动作。若需要更换模型，在 model.use 填写不同名称并安装对应模型包即可（模型名称和预览可参考插件提供的模型仓库地址）[\[145\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%2A%20GitHub%EF%BC%9Ahttps%3A%2F%2Fgithub.com%2FEYHN%2Fhexo,2.0)[\[141\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=model%3A%20use%3A%20live2d,300%20mobile%3A%20show%3A%20true%20react)。

ℹ️ **注意：**看板娘虽有趣，但会加载额外的模型和脚本资源，对页面性能有轻微影响。如果追求极致性能可以不加。此外，hexo-helper-live2d 插件对 PJAX 支持良好[\[146\]](https://blog.csdn.net/weixin_43868299/article/details/108949274#:~:text=live2d)，安知鱼主题默认PJAX开启，可正常兼容看板娘。如果发现看板娘在PJAX导航后消失等问题，可检查插件文档的FAQ。

- **音乐播放器（背景音乐/音乐馆）：**安知鱼主题内置了一个精美的**音乐播放器**功能，昵称“左下角音乐球”。它基于 MetingJS 和 APlayer，可以播放在线音乐。配置非常简单：在主题配置中找到 nav\_music 设置项，将其 enable 设为 true[\[147\]](https://docs.anheyu.com/advanced/#:~:text=,enable%3A%20true)。然后需要提供一个歌单来源，可以通过以下两种方式之一：
- **指定歌曲/歌单 ID 和源：**直接在配置里填写音乐平台的歌曲或歌单ID以及源。例如：

  nav\_music:\
  `  `enable: true\
  `  `console\_widescreen\_music: false  # 是否在宽屏状态下在控制台显示音乐列表\
  `  `id: 8152976493\
  `  `server: netease\
  `  `type: playlist

  以上示例表示使用网易云音乐的歌单，ID 为 8152976493[\[148\]](https://docs.anheyu.com/advanced/#:~:text=nav_music%3A%20enable%3A%20true)。server 可选 netease（网易云）、tencent（QQ音乐）等，type 指定为 playlist（歌单）或 song（单曲）等。设置后音乐球将加载该歌单的曲目。
- **提供歌单链接：**也可以用 all\_playlist 字段提供一个公开的歌单链接，主题会尝试解析其中的歌曲[\[149\]](https://docs.anheyu.com/advanced/#:~:text=match%20at%20L719%20console_widescreen_music%3A%20false,com%2Fn%2Fryqq%2Fplaylist%2F8802438608)。例如：

  all\_playlist: https://y.qq.com/n/ryqq/playlist/8802438608

  这是一个 QQ 音乐的歌单链接。配合 server: tencent，能让播放器抓取歌曲信息。

完成配置后重新启动，博客左下角会出现一个悬浮的圆形音乐按钮。点击后会弹出播放面板，列出歌曲列表。这个音乐播放器支持播放控制、进度、音量调节，以及背景歌词显示等。**Tip:** 如果希望音乐持续播放不因页面切换中断，需要依赖 PJAX 保持页面元素不刷新。安知鱼主题默认PJAX开启，音乐可以不中断播放，用户在浏览博客时音乐球会一直在左下角播放，非常酷炫。

- **打字效果（Typing 动效）：**所谓“打字机效果”通常指页面上文字一个字母一个字母渐显，仿佛有人正在打字。这通常通过引入 **typed.js** 库实现。安知鱼主题已经在内部包含了 typed.js（在 CDN 配置里可以看到 typed 的资源）[\[150\]](https://docs.anheyu.com/global/extra#:~:text=,medium_zoom)。主题可能将打字效果用于某些地方（比如加载欢迎语或某个 Banner 字句）。如果您想自定义打字效果，可以自行利用 typed.js。简单做法：在需要的页面或元素上添加相应 HTML 和配置，并把启动代码通过 inject 注入。例如，在首页添加一句动态字幕“Hello, World!”：

  <span id="typed-text"></span>\
  <script>\
  `  `document.addEventListener('DOMContentLoaded', function(){\
  `    `new Typed('#typed-text', {\
  `      `strings: ['Hello, World!', '欢迎来到我的博客'],\
  `      `typeSpeed: 100,\
  `      `backSpeed: 50,\
  `      `loop: true\
  `    `});\
  `  `});\
  </script>

  当然，前提是页面加载了 typed.js 脚本。幸运的是，主题CDN默认提供 typed.js，如未禁用CDN则可以直接使用。如果想全站某处使用打字效果，可以考虑在主题配置里开启欢迎语（welcome message），有些主题提供了内置的打字欢迎语配置项，可查看是否存在以启用。
- **代码高亮和显示优化：**安知鱼主题对代码块已经做了很多优化：多样的配色方案、行号显示、代码复制按钮等。之前已经提到可以在配置里选择代码主题。除此之外，还可以启用代码块**行号**和**复制**功能：Hexo 5+ 自带行号开关，在站点 \_config.yml 里将 highlight.line\_number 设为 true 即可（若使用 Prismjs 则另当别论）。而代码一键复制功能，安知鱼主题可能已经内置（通常在每个代码块右上角显示一个复制图标）。如果没有，可以考虑引入 hexo-prism-plugin 等。但据主题特性列表，[\[151\]](https://docs.anheyu.com/intro#:~:text=,%E2%9C%85%20%E4%B8%B0%E5%AF%8C%E5%A4%9A%E6%A0%B7%E5%8C%96%E7%9A%84%E6%A0%87%E7%AD%BE%E9%80%89%E9%A1%B9%E5%BF%AB%E9%80%9F%E6%9E%84%E5%BB%BA%E4%BD%A0%E6%83%B3%E8%A6%81%E7%9A%84%E5%8A%9F%E8%83%BD)提到主题支持“丰富的标签”和代码功能，所以一般无需额外配置。如果想要**代码折叠**功能（长代码块折叠显示），可能需要手动给相应代码块添加 <details> 标签，或者使用主题提供的短代码。安知鱼主题文档里有**短标签 Tag Plugins** 部分[\[152\]](https://docs.anheyu.com/global/extra#:~:text=%E7%9F%AD%E6%A0%87%E7%AD%BE%20Tag%20Plugins)[\[153\]](https://docs.anheyu.com/global/extra#:~:text=%E7%9F%AD%E6%A0%87%E7%AD%BE%E8%99%BD%E7%84%B6%E8%83%BD%E4%B8%BA%E4%B8%BB%E9%A2%98%E5%B8%A6%E6%9D%A5%E4%B8%80%E4%BA%9B%E9%A2%9D%E5%A4%96%E7%9A%84%E5%8A%9F%E8%83%BD%E5%92%8C%20UI%20%E6%96%B9%E9%9D%A2%E7%9A%84%E5%BC%BA%E5%8C%96%EF%BC%8C%E4%BD%86%E6%98%AF%EF%BC%8C%E7%9F%AD%E6%A0%87%E7%AD%BE%E4%B9%9F%E6%9C%89%E6%98%8E%E6%98%BE%E7%9A%84%E9%99%90%E5%88%B6%EF%BC%8C%E4%BD%BF%E7%94%A8%E6%97%B6%E8%AF%B7%E7%95%99%E6%84%8F%E3%80%82)，其中可能包含一些增强语法，例如可折叠文本、彩色文字等，可以挖掘利用来美化文章内容。
- **自定义字体图标：**主题自带了一些图标字体（Iconfont 和 FontAwesome），在菜单和社交按钮等处广泛使用[\[154\]](https://docs.anheyu.com/intro#:~:text=,%E2%9C%85%20%E6%94%AF%E6%8C%81%E9%AB%98%E9%80%9F%E7%BC%93%E5%AD%98%E7%9A%84swpp%EF%BC%8Cpwa%E7%89%B9%E6%80%A7)。如果您想使用更多自定义图标，可以通过**阿里巴巴矢量图标库**添加。安知鱼主题支持引入自己的阿里图标库：只需将你的图标库提供的 symbol.js 引用链接填到主题配置的 icons.ali\_iconfont\_js 字段，并设置 fontawesome: false（如果不使用 FontAwesome）[\[155\]](https://netlify.serms.top/posts/b9cf97e0#:~:text=anzhiyu%20%E4%B8%BB%E9%A2%98%E4%BC%98%E5%8C%96,%E6%98%AF%E5%90%A6%E5%90%AF%E7%94%A8fontawesome6%E5%9B%BE%E6%A0%87)。然后，在需要的地方使用图标时，按照主题要求的命名规范：**将图标名称加上前缀 anzhiyu-icon-** 即可。例如你想插入 GitHub 图标，主题内置的用法是 <i class="anzhiyu-icon-github"></i>[\[156\]](https://docs.anheyu.com/global/base#:~:text=%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95%EF%BC%8C%E5%B0%86%E5%9B%BE%E6%A0%87%E5%BA%93%E4%B8%AD%E7%9A%84%E5%9B%BE%E6%A0%87%E5%90%8D%E5%A4%8D%E5%88%B6%EF%BC%8C%E7%84%B6%E5%90%8E%E5%8A%A0%E4%B8%8A%E5%89%8D%E7%BC%80%20anzhiyu,)。如果是你自己图标库里的图标，比如名称为 icon-test，那使用时写 <i class="anzhiyu-icon-test"></i>。主题会自动加载对应的 SVG 图标。通过这种方式，可以方便地扩展导航菜单、社交链接等处的图标样式，而不需额外导入图片。
- **其他有趣的自定义：**安知鱼主题还支持许多魔改功能：比如**“小空调”彩蛋**（页面上挂个小风扇吹风的动画）、**点击烟花效果**（点击页面出现爆炸特效）、**文章标题挂件**（在文章标题旁显示萌萌的小图标）等等。这些往往需要引入自定义 JS 或 CSS 实现。主题可能已经内置了一些（例如点击爱心特效、烟花特效在 CDN 列表中能看到 click\_heart, fireworks 等脚本[\[157\]](https://docs.anheyu.com/global/extra#:~:text=,meting_js)）。要启用这些特效，可以在主题配置的 extend 或 optional 部分查找相应开关。如没有全局开关，则可通过 Inject 自己加入脚本。举例：若想实现“点击页面出现爱心”，可在配置里 Inject bottom 添加：

  <script src="https://npm.elemecdn.com/anzhiyu-theme-static@1.2.0/js/click\_heart.js"></script>

  这会加载一个封装好的点击爱心效果脚本（假设这个链接有效）。这样当用户点击页面空白处，就会有爱心漂浮。诸如此类的特效请根据喜好选择，切勿一股脑全部打开，以免干扰用户阅读。

总而言之，自定义功能是让博客展现个性的重要手段。使用安知鱼主题，您已经拥有丰富的内置功能，可通过简单配置开启。而对于主题未直接支持的创意，也可以利用 Hexo 强大的插件生态和前端技能自行加入。建议每次添加自定义效果后，在多端测试（PC和手机）确保兼容性。合理运用这些小功能，能让您的博客更加与众不同又不失专业。
## <a name="博客备份与迁移方法"></a>8. 博客备份与迁移方法
维护博客时，**定期备份**非常重要。Hexo 博客的备份主要是备份您博客的源文件（包括文章、页面、配置、主题等），以便在换电脑或发生意外时能迅速恢复。以下是备份和迁移的一些实用建议：

- **使用 Git 进行版本管理：**推荐将整个 Hexo 博客目录置于 Git 仓库中进行管理。这既是备份也是版本控制。您可以在本地初始化 Git 仓库，并将除 node\_modules/ 等临时目录外的文件全部纳入版本控制，然后推送到远程私人仓库（如 GitHub 私有仓库）。安知鱼作者本身就是采用**双仓库**方案：一个私有仓库保存 Hexo 源码（包含所有 Markdown、配置和主题），一个公有仓库保存生成的静态页面[\[158\]](https://blog.anheyu.com/posts/asdx.html#:~:text=,SiteToken%5D%E7%94%B3%E8%AF%B7%E5%88%B0%E7%9A%84%E4%BB%A4%E7%89%8C%E7%A0%81%20Site%20%E6%8C%87%E7%AB%99%E7%82%B9%EF%BC%8C%E6%95%99%E7%A8%8B%E4%B8%AD%E4%BC%9A%E6%9B%BF%E6%8D%A2%E6%88%90%20Github%E3%80%81Gitee%E3%80%81Coding)[\[159\]](https://blog.anheyu.com/posts/asdx.html#:~:text=%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%98%AF%20butterfly%20%E4%B8%BB%E9%A2%98%EF%BC%8C%E8%AE%B0%E5%BE%97%E6%9B%BF%E6%8D%A2%E6%9C%80%E5%90%8E%E4%B8%80%E8%A1%8C%E5%86%85%E5%AE%B9%E4%B8%BA%E4%BD%A0%E8%87%AA%E5%B7%B1%E5%BD%93%E5%89%8D%E4%BD%BF%E7%94%A8%E7%9A%84%E4%B8%BB%E9%A2%98%E3%80%82%202.%E6%8F%90%E4%BA%A4%E6%BA%90%E7%A0%81%E5%88%B0%E7%A7%81%E6%9C%89%E4%BB%93%E5%BA%93%60,git%20%E6%8C%87%E4%BB%A4%E9%87%8D%E8%AE%BE%E4%BB%93%E5%BA%93%E5%9C%B0%E5%9D%80%E3%80%82%E8%BF%99%E6%A0%B7%E5%9C%A8%E6%96%B0%E5%BB%BA%E4%BB%93%E5%BA%93%EF%BC%8C%E6%88%91%E4%BB%AC%E4%BB%8D%E6%97%A7%E5%8F%AF%E4%BB%A5%E4%BF%9D%E7%95%99%E7%8F%8D%E8%B4%B5%E7%9A%84%20commit%20history%EF%BC%8C%E4%BE%BF%E4%BA%8E%E7%89%88%E6%9C%AC%E5%9B%9E%E6%BB%9A%E3%80%82)。这种做法非常稳妥。您可以仿照：新建 GitHub 私有库如 “Hexo-blog-source”，将 Hexo 根目录下的内容（包括 source/、themes/、scaffolds/、\_config.yml、package.json 等）推送上去[\[160\]](https://blog.anheyu.com/posts/asdx.html#:~:text=%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E5%88%9B%E5%BB%BA%E4%B8%80%E4%B8%AA%E7%94%A8%E6%9D%A5%E5%AD%98%E6%94%BE%20%60Hexo%60%20%E5%8D%9A%E5%AE%A2%E6%BA%90%E7%A0%81%E7%9A%84%E7%A7%81%E6%9C%89%E4%BB%93%E5%BA%93%60,Image%207)[\[161\]](https://blog.anheyu.com/posts/asdx.html#:~:text=match%20at%20L517%201,SourceRepo%5D%60%E4%BA%86%E3%80%82%E6%BA%90%E7%A0%81%E4%BB%93%E5%BA%93%E4%B8%8D%E9%9C%80%E8%A6%81%E6%9C%89%20%60node_modules%20%60%E6%96%87%E4%BB%B6%E5%A4%B9%E3%80%82)。推送时注意 .gitignore 配置，通常会忽略 node\_modules/、public/ 等不需要的目录（默认 .gitignore 已包含）[\[162\]](https://blog.csdn.net/qq_36667170/article/details/118163563#:~:text=%E6%88%91%E4%BB%AC%E8%A6%81%E7%9F%A5%E9%81%93%EF%BC%8C%E5%9C%A8%E6%88%91%E4%BB%AC%E7%9A%84hexo%E5%8D%9A%E5%AE%A2%E6%96%87%E4%BB%B6%E4%B8%AD%E6%9C%89%E4%B8%80%E4%B8%AA%20)。确保 themes/ 下您正在使用的主题文件也在仓库中[\[163\]](https://blog.anheyu.com/posts/asdx.html#:~:text=1.%20%E6%98%AF%E5%90%A6%E5%B0%86%20%60node_modules%20%60%E4%B9%9F%E4%B8%8A%E4%BC%A0%E5%88%B0%E6%BA%90%E7%A0%81%E4%BB%93%E5%BA%93%60,%E6%96%87%E4%BB%B6%E5%A4%B9%E3%80%82)（否则新环境下没有主题源码无法生成页面）。通过 Git 版本库备份，您不仅随时可以获取历史记录，还能方便地同步多台设备上的博客源码。
- **手动备份压缩：**如果不方便使用 Git，也可以每次写完文章后将博客目录打包一次。例如将整个博客文件夹压缩成 zip 并存云盘。这种方式简单粗暴，但要记得频率及时，否则可能丢失最新文章。
- **迁移到新环境：**当您更换电脑或系统，需要在新环境恢复博客时，可以按照以下步骤操作：
- **搭建基础环境：**在新电脑上安装 Node.js、Git，以及 Hexo CLI（npm install -g hexo-cli）。Node 版本建议与原环境相近，以减少不兼容风险[\[164\]](https://cloud.tencent.com/developer/article/1835685#:~:text=)。
- **获取博客源码：**如果用了 Git，则将私有源码仓库 clone 到新电脑。例如 <NAME_EMAIL>:you/Hexo-blog-source.git。如果是手动备份压缩包，则解压得到博客文件夹[\[164\]](https://cloud.tencent.com/developer/article/1835685#:~:text=)。确保获取到的文件包括之前提到的所有关键部分。
- **安装依赖：**进入博客目录，在终端运行 npm install，这会根据 package.json 安装 Hexo 及所需插件依赖[\[165\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E5%A4%8D%E5%88%B6)。等待安装完成。
- **配置环境差异：**通常 Hexo 环境迁移只需以上步骤。如果您使用了 SSH 部署，还需要在新机器上配置 SSH Key（步骤类似之前在 GitHub Pages 部署时所做的，生成 key 并添加到 GitHub）[\[28\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%B8%89%E3%80%81%E9%85%8D%E7%BD%AE%20,QiQi_Blog%60%EF%BC%89%20%60Git%20Bash%20Here%60%20%E8%BE%93%E5%85%A5%E4%BB%A5%E4%B8%8B%E5%91%BD%E4%BB%A4)[\[166\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%24%20ssh,%2F%2F%E5%BC%95%E5%8F%B7%E9%87%8C%E9%9D%A2%E5%A1%AB%E5%86%99%E4%BD%A0%E7%9A%84%E9%82%AE%E7%AE%B1%E5%9C%B0%E5%9D%80%EF%BC%8C%E6%AF%94%E5%A6%82%E6%88%91%E7%9A%84%E6%98%AF1907065810%40qq.com)。另外检查 \_config.yml 中的路径设置（如本地图片路径）在新环境是否有效。一般来说换电脑不会影响这些配置。
- **验证运行：**执行 hexo clean && hexo g && hexo s 本地预览，确认博客一切正常。如果有报错，根据错误信息安装缺少的插件或修复配置。例如常见的就是忘记安装某插件导致 “Unknown deployer” 等，可重新安装对应插件。

完成上述，新电脑环境下即可像原来一样写作和部署了。概括地讲，**Hexo 迁移很类似复制粘贴**：拷贝博客源文件，安装环境，运行生成即可[\[167\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E5%A6%82%E4%BD%95%E5%B0%86hexo%E8%BF%81%E5%88%B0%E4%B8%80%E5%8F%B0%E6%96%B0%E7%94%B5%E8%84%91%E4%B8%8A%20,%E8%A7%A3%E5%8E%8B%E4%BB%A5%E5%90%8E%E5%9C%A8)。网上很多教程把迁移搞得很复杂，其实核心就是保留好源文件和确保依赖安装到位[\[168\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E7%BD%91%E4%B8%8A%E7%9A%84%E5%85%B6%E4%BB%96%E6%95%99%E7%A8%8B%E9%83%BD%E5%BE%88%E5%A4%8D%E6%9D%82%E7%B9%81%E7%90%90)[\[165\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E5%A4%8D%E5%88%B6)。

- **定期备份数据库文件：**Hexo 本身无数据库，一切内容都在文件里。不过如果用了某些插件（如 hexo-admin 编辑器）会生成 .db 文件用于缓存数据，可以备份一下。另外 Valine 等评论数据因为存第三方云服务，就不在本地备份范畴了，需要在其后台另行导出（如果在意评论数据的话）。
- **主题更新与备份：**如果您对主题源码进行了修改（比如魔改了一些样式或功能），那么每次主题升级前务必备份修改过的文件，并在升级后进行合并。这种情况最好利用 Git 的分支来管理，以便清晰追踪更改。这也是官方建议使用覆盖配置 \_config.anzhiyu.yml 的原因，可以避免升级时配置丢失[\[169\]](https://docs.anheyu.com/initall#:~:text=%E8%A6%86%E7%9B%96%E9%85%8D%E7%BD%AE)[\[170\]](https://docs.anheyu.com/initall#:~:text=%E6%B3%A8%E6%84%8F%EF%BC%9A)。

简而言之，**良好的备份习惯**应做到：文章内容有版本管理（避免误删）、环境更换可快速重建、出现问题可回滚特定版本。借助 Git 等工具可以大大简化这工作。当您的博客内容越来越多，这些备份和迁移方法将保障博客的安全和持续。
## <a name="常见问题及解决方案"></a>9. 常见问题及解决方案
在搭建和使用博客过程中，难免遇到一些问题。以下汇总安知鱼主题下常见的几个问题和对应的解决方案：

- **Q1: 本地生成时出现错误 “wordcount is not a function”**\
  **A:** 这是因为开启了文章字数统计功能但未安装所需插件[\[51\]](https://docs.anheyu.com/faq#:~:text=wordcount%20is%20not%20a%20function)。Hexo 主题统计字数通常需要 hexo-wordcount 或 hexo-symbols-count-time 插件支撑。安知鱼主题默认提供字数统计开关，如果开启了请确保安装插件。解决办法：进入博客目录，执行：

  npm install hexo-wordcount --save

  安装成功后重新生成即可[\[53\]](https://docs.anheyu.com/faq#:~:text=%E4%BA%A7%E7%94%9F%E5%8E%9F%E5%9B%A0%EF%BC%9A%E5%BC%80%E5%90%AF%E4%BA%86wordcount%E7%9A%84%E5%AD%97%E6%95%B0%E7%BB%9F%E8%AE%A1%EF%BC%8C%E4%BD%86%E6%98%AF%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E5%AF%B9%E5%BA%94%E6%8F%92%E4%BB%B6%E3%80%82)。若使用 yarn 也可以 yarn add hexo-wordcount。安装后插件会在生成页面时为每篇文章注入字数和阅读时长统计信息。
- **Q2: 为什么我的博客页面显示得特别大？**\
  **A:** 如果您在 Windows 等分辨率较低的屏幕上浏览，可能会觉得页面元素偏大。这其实不是 bug，而是**设计使然**——安知鱼主题是在 16寸 MacBook Pro 的高分屏下以 110% 缩放开发的[\[171\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%BA%E4%BB%80%E4%B9%88%E6%88%91%E7%9A%84%E5%8D%9A%E5%AE%A2%E6%98%BE%E7%A4%BA%E7%9A%84%E5%BE%88%E5%A4%A7%EF%BC%9F)。因此在分辨率不高的设备上看会显得放大。解决很简单：按住 Ctrl 并滚动鼠标缩小页面（或按 Ctrl + - 快捷键）调整浏览器缩放比例，直到感觉合适为止[\[171\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%BA%E4%BB%80%E4%B9%88%E6%88%91%E7%9A%84%E5%8D%9A%E5%AE%A2%E6%98%BE%E7%A4%BA%E7%9A%84%E5%BE%88%E5%A4%A7%EF%BC%9F)。浏览器会记忆您的缩放设置，下次访问该站时保持相同比例。这个调整不影响别人访问您网站，他们会根据各自设备自动适配。如果您想从主题上统一调小显示比例，可以尝试修改主题的根字体大小（可能需要在 CSS 中调整 html { font-size: xx; }），但不建议这样做，因为可能影响其他元素布局。
- **Q3: 如何更改首页顶部的大图（封面图）？**\
  **A:** 首页顶部大图（也就是所谓的 banner 图或 top\_img）通常由主题默认提供风景图或纯色背景。要换成自己喜欢的图片，编辑主题配置文件，在 index\_img 字段设置背景样式[\[172\]](https://docs.anheyu.com/faq#:~:text=%E7%AD%94%EF%BC%9A%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20)。例如：

  index\_img: "background: url('https://your.image.link/here.jpg') top/cover no-repeat"

  上面示例中，用您自己的图片链接替换其中 URL，并附加了显示方式：top/cover no-repeat 表示顶部对齐、覆盖填充、无需重复[\[173\]](https://docs.anheyu.com/faq#:~:text=yaml)。您也可以按照这个格式列出多张图片，主题会随机选用。如果不想首页显示封面图片，将该项设置为 false 即可[\[174\]](https://www.fomal.cc/posts/3451f874.html#:~:text=Hexo%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B%28%E4%B8%89%29%20,%EF%BC%9B%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%83%B3%E5%9C%A8%E9%A6%96%E9%A1%B5%E6%98%BE%E7%A4%BAcover%EF%BC%8C%E5%8F%AF%E4%BB%A5%E8%AE%BE%E7%BD%AE%E4%B8%BA%20false%20%E3%80%82%20%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20_config)。修改保存后重新生成部署，首页就会展现新图片风格了。
- **Q4: 本地运行或部署时提示某个插件找不到/报错怎么办？**\
  **A:** 这种情况多半是遗漏安装某插件或版本不兼容。比如常见的有 Error: highlight.js ... 之类，可能是代码高亮插件有冲突。如果遇到插件类错误，首先检查**是否安装**了对应插件（package.json 里有但本地没装的话运行 npm install 补齐）。其次检查插件版本是否满足 Hexo 要求，例如 Hexo 5.x 以上需使用新版渲染器。如果是主题自带功能报错，可到主题仓库查看 issue 或更新日志，看看是否有已知问题和补丁。如果实在无法判断，可以尝试执行 hexo clean 清缓存后再生成，有时缓存问题也会导致莫名错误。
- **Q5: 社交图标或菜单图标不显示？**\
  **A:** 如果您新增了自定义图标却不显示，很可能是**图标字体**没有正确引用。安知鱼主题支持阿里图标和 FontAwesome，需要在配置中启用。其中 icons.ali\_iconfont\_js 应该填入你自己图标库的 symbol.js地址[\[155\]](https://netlify.serms.top/posts/b9cf97e0#:~:text=anzhiyu%20%E4%B8%BB%E9%A2%98%E4%BC%98%E5%8C%96,%E6%98%AF%E5%90%A6%E5%90%AF%E7%94%A8fontawesome6%E5%9B%BE%E6%A0%87)。填好并确保 fontawesome 开关正确（默认内置了一些 FontAwesome 图标，如果设为 false 则只用阿里图标）。然后使用图标时带上前缀anzhiyu-icon-[\[156\]](https://docs.anheyu.com/global/base#:~:text=%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95%EF%BC%8C%E5%B0%86%E5%9B%BE%E6%A0%87%E5%BA%93%E4%B8%AD%E7%9A%84%E5%9B%BE%E6%A0%87%E5%90%8D%E5%A4%8D%E5%88%B6%EF%BC%8C%E7%84%B6%E5%90%8E%E5%8A%A0%E4%B8%8A%E5%89%8D%E7%BC%80%20anzhiyu,)。举例：假设您在阿里图标库中新建了图标 “weibo”，在引入后，用 <i class="anzhiyu-icon-weibo"></i> 插入即可。如果图标仍不显示，按F12检查控制台，看是否有资源加载404错误，可能是图标地址不对或者网络原因。调整正确后刷新即可。
- **Q6: 部署到 GitHub Pages 后，页面样式错乱或不更新？**\
  **A:** 这通常是缓存导致。GitHub Pages 有时对静态文件有缓存，导致您更新部署后立刻访问看到的仍是旧版本。可尝试强制刷新（Ctrl+F5）或清浏览器缓存。如等待一段时间依然不对，检查是否正确执行了 hexo clean 导致已生成文件。另一个可能原因是您的自定义域未正确配置，导致引用 CSS/JS 路径错误。确保 \_config.yml 里的 url 和 root 配置和实际访问域名一致。如果使用自定义域，root 应该为 / 而不是 /<username>.github.io/。这个问题解决后再部署，一般样式就正常了。

如果以上没有涵盖您的问题，不妨查看安知鱼主题官方文档的**常见问题**部分[\[175\]](https://docs.anheyu.com/advanced/#:~:text=%E8%BF%9B%E9%98%B6%E9%85%8D%E7%BD%AE)或寻求社区支持。主题作者的 QQ交流群也提供了提问交流的渠道[\[176\]](https://docs.anheyu.com/intro#:~:text=%E4%BA%A4%E6%B5%81%E7%BE%A4)。遇到问题时，多利用搜索引擎，很多 Hexo/主题相关的问题都能在网络上找到前人经验。相信在不断折腾中，您的博客会越来越完善！[\[177\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%BA%E4%BB%80%E4%B9%88%E6%88%91%E7%9A%84%E5%8D%9A%E5%AE%A2%E6%98%BE%E7%A4%BA%E7%9A%84%E5%BE%88%E5%A4%A7%EF%BC%9F)[\[178\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%8D%E8%A6%81%E6%85%8C)（以上两个小问题和解答也来自官方文档的常见问答节选，供参考。）

-----
*参考资料：本指南内容整理自安知鱼主题官方文档[*\[179\]*](https://docs.anheyu.com/intro#:~:text=%E5%AE%89%E7%9F%A5%E9%B1%BC%E4%B8%BB%E9%A2%98%20%E6%98%AF%E5%9F%BA%E4%BA%8E%20Hexo%20%E7%9A%84%E4%B8%80%E6%AC%BE%20%E7%AE%80%E5%8D%95%E3%80%81%E7%BE%8E%E4%B8%BD,%E7%9A%84%E4%B8%BB%E9%A2%98%EF%BC%8C%E7%94%B1%20%E5%AE%89%E7%9F%A5%E9%B1%BC%20%E8%B4%9F%E8%B4%A3%E5%BC%80%E5%8F%91%E4%B8%8E%E7%BB%B4%E6%8A%A4%E3%80%82)[*\[180\]*](https://docs.anheyu.com/initall#:~:text=%E6%B8%A9%E9%A6%A8%E6%8F%90%E7%A4%BA)以及安知鱼博客的经验分享，涵盖 Hexo 博客搭建到优化的方方面面。如需更详细的信息，请参阅安知鱼主题 GitHub 仓库和文档。愿您的博客搭建之旅顺利愉快！*

-----
<a name="citations"></a>[\[1\]](https://blog.anheyu.com/posts/ddae.html#:~:text=node) [\[2\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%AE%89%E8%A3%85%20Git) [\[15\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E5%8D%B3%E5%8F%AF%E7%99%BB%E5%BD%95%20http%3A%2F%2Flocalhost%3A4000%2F%20%E6%9F%A5%E7%9C%8B%E6%95%88%E6%9E%9C) [\[24\]](https://blog.anheyu.com/posts/ddae.html#:~:text=Github%20Pages%20%E5%8F%AF%E4%BB%A5%E8%A2%AB%E8%AE%A4%E4%B8%BA%E6%98%AF%E7%94%A8%E6%88%B7%E7%BC%96%E5%86%99%E7%9A%84%E3%80%81%E6%89%98%E7%AE%A1%E5%9C%A8%20github%20%E4%B8%8A%E7%9A%84%E9%9D%99%E6%80%81%E7%BD%91%E9%A1%B5%E3%80%82%E4%BD%BF%E7%94%A8,Github%20Pages%20%E5%8F%AF%E4%BB%A5%E4%B8%BA%E4%BD%A0%E6%8F%90%E4%BE%9B%E4%B8%80%E4%B8%AA%E5%85%8D%E8%B4%B9%E7%9A%84%E6%9C%8D%E5%8A%A1%E5%99%A8%EF%BC%8C%E5%85%8D%E5%8E%BB%E4%BA%86%E8%87%AA%E5%B7%B1%E6%90%AD%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%99%A8%E5%92%8C%E5%86%99%E6%95%B0%E6%8D%AE%E5%BA%93%E7%9A%84%E9%BA%BB%E7%83%A6%E3%80%82%E6%AD%A4%E5%A4%96%E8%BF%98%E5%8F%AF%E4%BB%A5%E7%BB%91%E5%AE%9A%E8%87%AA%E5%B7%B1%E7%9A%84%E5%9F%9F%E5%90%8D%E3%80%82) [\[25\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%B8%80%E3%80%81%E7%99%BB%E5%BD%95%20Github%20%E6%89%93%E5%BC%80%E8%87%AA%E5%B7%B1%E7%9A%84%E9%A1%B9%E7%9B%AE%20yourname) [\[26\]](https://blog.anheyu.com/posts/ddae.html#:~:text=) [\[27\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%BA%94%E3%80%81%E6%BB%91%E5%88%B0%E6%9C%80%E4%B8%8B%E9%9D%A2%2C%E6%8C%89%E4%B8%8B%E5%9B%BE%E4%BF%AE%E6%94%B9%20_config) [\[28\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%B8%89%E3%80%81%E9%85%8D%E7%BD%AE%20,QiQi_Blog%60%EF%BC%89%20%60Git%20Bash%20Here%60%20%E8%BE%93%E5%85%A5%E4%BB%A5%E4%B8%8B%E5%91%BD%E4%BB%A4) [\[29\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%9B%9B%E3%80%81%E5%9C%A8%20GitHub%20%E8%B4%A6%E6%88%B7%E4%B8%AD%E6%B7%BB%E5%8A%A0%E4%BD%A0%E7%9A%84%E5%85%AC%E9%92%A5) [\[30\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E6%89%A7%E8%A1%8C%E5%AE%8C%E4%B9%8B%E5%90%8E%E4%BC%9A%E8%AE%A9%E4%BD%A0%E8%BE%93%E5%85%A5%E4%BD%A0%E7%9A%84%20Github%20%E7%9A%84%E8%B4%A6%E5%8F%B7%E5%92%8C%E5%AF%86%E7%A0%81%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%AD%A4%E6%97%B6%E6%8A%A5%E4%BB%A5%E4%B8%8B%E9%94%99%E8%AF%AF%EF%BC%8C%E8%AF%B4%E6%98%8E%E4%BD%A0%E7%9A%84%20deployer%20%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E6%88%90%E5%8A%9F) [\[31\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E5%85%AD%E3%80%81%E5%9C%A8%20Hexo%20%E6%96%87%E4%BB%B6%E5%A4%B9%E4%B8%8B%E5%88%86%E5%88%AB%E6%89%A7%E8%A1%8C%E4%BB%A5%E4%B8%8B%E5%91%BD%E4%BB%A4) [\[32\]](https://blog.anheyu.com/posts/ddae.html#:~:text=%E4%BD%A0%E7%9A%84%E5%8D%9A%E5%AE%A2%E5%B0%B1%E4%BC%9A%E9%83%A8%E7%BD%B2%E5%88%B0%20Github%20%E4%B8%8A%E4%BA%86) 使用 Github Pages 和 Hexo 搭建自己的独立博客 | 安知鱼

<https://blog.anheyu.com/posts/ddae.html>

[\[3\]](https://docs.anheyu.com/intro#:~:text=%E9%A2%84%E8%A7%88%3A%20AnZhiYu%20) [\[21\]](https://docs.anheyu.com/intro#:~:text=%E5%A6%82%E6%9E%9C%E8%83%BD%E7%BB%99%E6%88%91%E4%B8%80%E4%B8%AA%20star%20%E9%82%A3%E5%B0%86%E6%98%AF%E5%AF%B9%E6%88%91%E8%8E%AB%E5%A4%A7%E7%9A%84%E9%BC%93%E5%8A%B1%E3%80%82%E4%BD%BF%E7%94%A8%E8%BF%99%E4%B8%AA%E4%B8%BB%E9%A2%98%E4%B9%8B%E5%89%8D%EF%BC%8C%E4%BD%A0%E5%BA%94%E8%AF%A5%E6%98%8E%E7%99%BD%E5%AE%83%E6%98%AF%E4%B8%80%E4%B8%AAHexo%E4%B8%BB%E9%A2%98%EF%BC%8C%E5%AE%83%E7%9A%84%E5%9F%BA%E6%9C%AC%E9%80%BB%E8%BE%91%E7%A6%BB%E4%B8%8D%E5%BC%80Hexo%EF%BC%8C%E5%85%B3%E4%BA%8E%E5%A6%82%E4%BD%95%E6%96%B0%E5%BB%BA%E5%88%86%E7%B1%BB%EF%BC%8C%E5%A6%82%E4%BD%95%E6%96%B0%E5%BB%BA%E6%A0%87%E7%AD%BE%E8%BF%99%E4%BA%9B%20%E9%97%AE%E9%A2%98%E5%BA%94%E8%AF%A5%E5%9C%A8%E4%BD%BF%E7%94%A8%E4%B9%8B%E5%89%8D%E5%B0%B1%E4%BB%8E%E4%BA%92%E8%81%94%E7%BD%91%E6%88%96%E5%AE%98%E6%96%B9%E6%96%87%E6%A1%A3%E4%BA%86%E8%A7%A3%E8%AF%A6%E6%83%85%E3%80%82) [\[126\]](https://docs.anheyu.com/intro#:~:text=%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7) [\[151\]](https://docs.anheyu.com/intro#:~:text=,%E2%9C%85%20%E4%B8%B0%E5%AF%8C%E5%A4%9A%E6%A0%B7%E5%8C%96%E7%9A%84%E6%A0%87%E7%AD%BE%E9%80%89%E9%A1%B9%E5%BF%AB%E9%80%9F%E6%9E%84%E5%BB%BA%E4%BD%A0%E6%83%B3%E8%A6%81%E7%9A%84%E5%8A%9F%E8%83%BD) [\[154\]](https://docs.anheyu.com/intro#:~:text=,%E2%9C%85%20%E6%94%AF%E6%8C%81%E9%AB%98%E9%80%9F%E7%BC%93%E5%AD%98%E7%9A%84swpp%EF%BC%8Cpwa%E7%89%B9%E6%80%A7) [\[176\]](https://docs.anheyu.com/intro#:~:text=%E4%BA%A4%E6%B5%81%E7%BE%A4) [\[179\]](https://docs.anheyu.com/intro#:~:text=%E5%AE%89%E7%9F%A5%E9%B1%BC%E4%B8%BB%E9%A2%98%20%E6%98%AF%E5%9F%BA%E4%BA%8E%20Hexo%20%E7%9A%84%E4%B8%80%E6%AC%BE%20%E7%AE%80%E5%8D%95%E3%80%81%E7%BE%8E%E4%B8%BD,%E7%9A%84%E4%B8%BB%E9%A2%98%EF%BC%8C%E7%94%B1%20%E5%AE%89%E7%9F%A5%E9%B1%BC%20%E8%B4%9F%E8%B4%A3%E5%BC%80%E5%8F%91%E4%B8%8E%E7%BB%B4%E6%8A%A4%E3%80%82) 主题简介 | 安知鱼主题官方文档

<https://docs.anheyu.com/intro>

[\[4\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%80) [\[5\]](https://docs.anheyu.com/initall#:~:text=bash) [\[6\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%BA%8C) [\[7\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%89) [\[8\]](https://docs.anheyu.com/initall#:~:text=%E6%89%93%E5%BC%80%20Hexo%20%E6%A0%B9%E7%9B%AE%E5%BD%95%E4%B8%8B%E7%9A%84%20,anzhiyu) [\[9\]](https://docs.anheyu.com/initall#:~:text=%E5%AE%89%E8%A3%85%20pug%20%E5%92%8C%20stylus%20%E6%B8%B2%E6%9F%93%E6%8F%92%E4%BB%B6) [\[10\]](https://docs.anheyu.com/initall#:~:text=%E6%97%A0%E6%B3%95%E5%AE%89%E8%A3%85%E5%8F%AF%E4%BB%A5%E4%BD%BF%E7%94%A8cnpm%E8%BF%9B%E8%A1%8C%E5%AE%89%E8%A3%85) [\[11\]](https://docs.anheyu.com/initall#:~:text=bash) [\[12\]](https://docs.anheyu.com/initall#:~:text=%E6%B3%A8%E6%84%8F%EF%BC%9A) [\[13\]](https://docs.anheyu.com/initall#:~:text=,debug%60%20%E6%9F%A5%E7%9C%8B%E5%91%BD%E4%BB%A4%E8%A1%8C%E8%BE%93%E5%87%BA%E3%80%82) [\[14\]](https://docs.anheyu.com/initall#:~:text=%E6%9C%AC%E5%9C%B0%E5%90%AF%E5%8A%A8%20hexo) [\[16\]](https://docs.anheyu.com/initall#:~:text=,%E7%9B%AE%E5%BD%95%EF%BC%9B) [\[17\]](https://docs.anheyu.com/initall#:~:text=%E6%96%B9%E5%BC%8F%E4%B8%80) [\[18\]](https://docs.anheyu.com/initall#:~:text=1) [\[19\]](https://docs.anheyu.com/initall#:~:text=) [\[20\]](https://docs.anheyu.com/initall#:~:text=%E5%9C%A8%E5%8D%9A%E5%AE%A2%E7%9B%AE%E5%BD%95%E4%B8%8B%E6%89%A7%E8%A1%8C%E5%91%BD%E4%BB%A4%EF%BC%9A) [\[22\]](https://docs.anheyu.com/initall#:~:text=%E5%8F%A6%E5%A4%96%E6%9C%AC%E6%8C%87%E5%8D%97%E4%BB%85%E5%8C%85%E5%90%AB%E4%B8%BB%E9%A2%98%E8%8C%83%E5%9B%B4%E5%86%85%E7%9A%84%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%EF%BC%8C%E5%A6%82%E6%9E%9C%E6%98%AF%20Hexo%20%E7%9A%84%E4%BD%BF%E7%94%A8%E6%88%96%E8%80%85%20Hexo%20%E6%8F%92%E4%BB%B6%E7%9A%84%E4%BD%BF%E7%94%A8%EF%BC%8C%E8%AF%B7%E6%9F%A5%E9%98%85%E5%90%84%E8%87%AA%E7%9A%84%E6%96%87%E6%A1%A3%E3%80%82) [\[35\]](https://docs.anheyu.com/initall#:~:text=,%E6%B8%85%E9%99%A4%E6%9C%AC%E5%9C%B0%E7%BC%93%E5%AD%98%EF%BC%9B) [\[36\]](https://docs.anheyu.com/initall#:~:text=) [\[127\]](https://docs.anheyu.com/initall#:~:text=,%E7%89%88%E6%9C%AC%E5%8F%B7%E9%87%8A%E4%B9%89) [\[169\]](https://docs.anheyu.com/initall#:~:text=%E8%A6%86%E7%9B%96%E9%85%8D%E7%BD%AE) [\[170\]](https://docs.anheyu.com/initall#:~:text=%E6%B3%A8%E6%84%8F%EF%BC%9A) [\[180\]](https://docs.anheyu.com/initall#:~:text=%E6%B8%A9%E9%A6%A8%E6%8F%90%E7%A4%BA) 主题安装 | 安知鱼主题官方文档

<https://docs.anheyu.com/initall>

[\[23\]](https://docs.anheyu.com/page/404#:~:text=404%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE) 404页面配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/page/404>

[\[33\]](https://blog.anheyu.com/posts/b228.html#:~:text=) [\[34\]](https://blog.anheyu.com/posts/b228.html#:~:text=%E5%B7%A5%E4%BD%9C%E6%B5%81) hexo博客工作流CI（一键部署的快乐） | 安知鱼

<https://blog.anheyu.com/posts/b228.html>

[\[37\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=) [\[38\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E9%BB%98%E8%AE%A4%E8%AF%AD%E8%A8%80%E6%98%AF%20en) [\[39\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=) [\[40\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=menu%3A%20%E6%96%87%E7%AB%A0%3A%20%E9%9A%A7%E9%81%93%3A%20%2Farchives%2F%20,tags) [\[41\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E9%9F%B3%E4%B9%90%E9%A6%86%3A%20%2Fmusic%2F%20%7C%7C%20anzhiyu,fan) [\[42\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E5%BF%85%E9%A1%BB%E6%98%AF%20%60%2Fxxx%2F%60%EF%BC%8C%E5%90%8E%E9%9D%A2%60) [\[49\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=) [\[50\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=%E4%BF%AE%E6%94%B9%20) [\[67\]](https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html#:~:text=archives_enable%3A%20true%20,right%20%2C%20both%20position%3A%20both) 站点基础配置(一) | 安知鱼主题指南

<https://gavinblog.github.io/anzhiyu-docs/site-configuration1.html>

[\[43\]](https://docs.anheyu.com/page/links#:~:text=menu%3A%20,tags) [\[44\]](https://docs.anheyu.com/page/links#:~:text=%E5%85%B3%E4%BA%8E%3A%20,prints1) [\[76\]](https://docs.anheyu.com/page/links#:~:text=1) [\[77\]](https://docs.anheyu.com/page/links#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20) [\[78\]](https://docs.anheyu.com/page/links#:~:text=5.%20%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%20%60source,link.yml%60%EF%BC%8C%E8%BE%93%E5%85%A5%EF%BC%9A) [\[79\]](https://docs.anheyu.com/page/links#:~:text=yml) [\[80\]](https://docs.anheyu.com/page/links#:~:text=,static%401.0.4%2Fimg%2Favatar.jpg%20descr%3A%20%E7%94%9F%E6%B4%BB%E6%98%8E%E6%9C%97%EF%BC%8C%E4%B8%87%E7%89%A9%E5%8F%AF%E7%88%B1) [\[81\]](https://docs.anheyu.com/page/links#:~:text=class_name%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E5%90%8D%20class_desc%E3%80%96%E5%8F%AF%E9%80%89%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%86%E7%B1%BB%E6%8F%8F%E8%BF%B0%20flink_style%E3%80%96%E5%BF%85%E5%A1%AB%E3%80%97,link_list%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%88%97%E8%A1%A8%20link_list.name%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%90%8D%E7%A7%B0%20link_list.link%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E9%93%BE%E6%8E%A5%20link_list.avatar%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E5%8F%8B%E9%93%BE%E5%A4%B4%E5%83%8F) [\[82\]](https://docs.anheyu.com/page/links#:~:text=link_list,%EF%BC%8C%E6%8F%90%E4%BE%9B%E4%BA%86%E4%B8%A4%E4%B8%AA%E5%BF%AB%E6%8D%B7%E9%A2%9C%E8%89%B2%E9%80%89%E9%A1%B9%E5%88%86%E5%88%AB%E6%98%AF%20%60vip%20%60%E5%92%8C%20%60speed) [\[83\]](https://docs.anheyu.com/page/links#:~:text=menu%3A%20,tags) [\[84\]](https://docs.anheyu.com/page/links#:~:text=%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E4%B8%AD%20) [\[85\]](https://docs.anheyu.com/page/links#:~:text=,n) 友情链接配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/page/links>

[\[45\]](https://docs.anheyu.com/advanced/#:~:text=%23%20%E9%A1%B9%E7%9B%AE%E5%9C%B0%E5%9D%80%EF%BC%9Ahttps%3A%2F%2Fgithub.com%2Fanzhiyu,%E6%95%B4%E7%AF%87%E6%96%87%E7%AB%A0%E8%B7%9F%E9%9A%8Fcover%E4%BF%AE%E6%94%B9%E4%B8%BB%E8%89%B2%E8%B0%83) [\[58\]](https://docs.anheyu.com/advanced/#:~:text=,%E9%A6%96%E9%A1%B5%E9%A1%B6%E9%83%A8%203%20%E5%A4%A7%E5%88%86%E7%B1%BB%E9%85%8D%E7%BD%AE) [\[59\]](https://docs.anheyu.com/advanced/#:~:text=,%E5%8A%A8%E6%95%88%E6%8E%A7%E5%88%B6) [\[92\]](https://docs.anheyu.com/advanced/#:~:text=%E7%95%99%E8%A8%80%E6%9D%BF%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE) [\[93\]](https://docs.anheyu.com/advanced/#:~:text=%E6%88%91%E7%9A%84%E8%A3%85%E5%A4%87%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE) [\[94\]](https://docs.anheyu.com/advanced/#:~:text=%E5%85%B3%E4%BA%8E%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE) [\[95\]](https://docs.anheyu.com/advanced/#:~:text=%E9%9F%B3%E4%B9%90%E9%A6%86%E9%A1%B5%E9%85%8D%E7%BD%AE) [\[106\]](https://docs.anheyu.com/advanced/#:~:text=,%E6%B7%B1%E8%89%B2%E6%A8%A1%E5%BC%8F%E7%B2%92%E5%AD%90%E6%95%88%E6%9E%9Ccanvas) [\[128\]](https://docs.anheyu.com/advanced/#:~:text=,comment%20count%20in%20post%27s%20top_img) [\[129\]](https://docs.anheyu.com/advanced/#:~:text=Twikoo) [\[130\]](https://docs.anheyu.com/advanced/#:~:text=,envId%3A%20region%3A%20visitor%3A%20false%20option) [\[131\]](https://docs.anheyu.com/advanced/#:~:text=) [\[132\]](https://docs.anheyu.com/advanced/#:~:text=,load%20when%20comment%20element%20enters) [\[133\]](https://docs.anheyu.com/advanced/#:~:text=match%20at%20L280%20waline%3A%20serverURL%3A,waline%20background%20pageview%3A%20false) [\[134\]](https://docs.anheyu.com/advanced/#:~:text=bg%3A%20,false) [\[136\]](https://docs.anheyu.com/advanced/#:~:text=yml) [\[137\]](https://docs.anheyu.com/advanced/#:~:text=valine%3A%20appId%3A%20,valine%20background%20visitor%3A%20false) [\[138\]](https://docs.anheyu.com/advanced/#:~:text=yml) [\[139\]](https://docs.anheyu.com/advanced/#:~:text=%E5%BC%80%E5%90%AF%E8%AF%84%E8%AE%BA%E9%9C%80%E8%A6%81%E5%9C%A8%20comments) [\[147\]](https://docs.anheyu.com/advanced/#:~:text=,enable%3A%20true) [\[148\]](https://docs.anheyu.com/advanced/#:~:text=nav_music%3A%20enable%3A%20true) [\[149\]](https://docs.anheyu.com/advanced/#:~:text=match%20at%20L719%20console_widescreen_music%3A%20false,com%2Fn%2Fryqq%2Fplaylist%2F8802438608) [\[175\]](https://docs.anheyu.com/advanced/#:~:text=%E8%BF%9B%E9%98%B6%E9%85%8D%E7%BD%AE) 进阶配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/advanced/>

[\[46\]](https://docs.anheyu.com/global/extra#:~:text=,6%20pm%20to%206%20am) [\[47\]](https://docs.anheyu.com/global/extra#:~:text=button%3A%20true%20,value%20is%206%20and%2018) [\[48\]](https://docs.anheyu.com/global/extra#:~:text=%E5%8F%82%E6%95%B0%20%E8%A7%A3%E9%87%8A%20button%20%E6%98%AF%E5%90%A6%E5%9C%A8%E5%8F%B3%E4%B8%8B%E8%A7%92%E6%98%BE%E7%A4%BA%E6%97%A5%E5%A4%9C%E6%A8%A1%E5%BC%8F%E5%88%87%E6%8D%A2%E6%8C%89%E9%92%AE%20autoChangeMode,dark%20mode%20autoChangeMode%3A%20false%20%E5%8F%96%E6%B6%88%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2) [\[54\]](https://docs.anheyu.com/global/extra#:~:text=busuanzi%3A%20site_uv%3A%20true%20site_pv%3A%20true,page_pv%3A%20true) [\[55\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L342%20,%E7%9A%84%20CDN%20%E4%B8%AD%E7%9A%84%20option%20%E8%BF%9B%E8%A1%8C%E4%BF%AE%E6%94%B9) [\[56\]](https://docs.anheyu.com/global/extra#:~:text=owner%3A%20enable%3A%20true%20since%3A%202020,%E4%B8%8A%E7%8F%AD%E6%91%B8%E9%B1%BC%E4%B8%AD.svg) [\[57\]](https://docs.anheyu.com/global/extra#:~:text=offduty_description%3A%20%E4%B8%8B%E7%8F%AD%E4%BA%86%E5%B0%B1%E8%AF%A5%E5%BC%80%E5%BC%80%E5%BF%83%E5%BF%83%E7%9A%84%E7%8E%A9%E8%80%8D%EF%BC%8C%E5%98%BF%E5%98%BF~%20,%E5%BE%BD%E6%A0%87%E6%8F%90%E7%A4%BA%E8%AF%AD) [\[60\]](https://docs.anheyu.com/global/extra#:~:text=%E4%BD%BF%E7%94%A8pjax%E5%90%8E%EF%BC%8C%E4%B8%80%E4%BA%9B%E4%B8%AA%E5%88%AB%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E7%9A%84js%2Fcss%EF%BC%8C%E5%B0%86%E4%BC%9A%E6%94%B9%E4%B8%BA%E6%89%80%E6%9C%89%E9%A1%B5%E9%9D%A2%E9%83%BD%E5%8A%A0%E8%BD%BD) [\[61\]](https://docs.anheyu.com/global/extra#:~:text=PWA) [\[62\]](https://docs.anheyu.com/global/extra#:~:text=1,offline.config.cjs%60%20%E6%96%87%E4%BB%B6%EF%BC%8C%E5%B9%B6%E5%A2%9E%E5%8A%A0%E4%BB%A5%E4%B8%8B%E5%86%85%E5%AE%B9%E3%80%82) [\[63\]](https://docs.anheyu.com/global/extra#:~:text=js) [\[64\]](https://docs.anheyu.com/global/extra#:~:text=CDN) [\[65\]](https://docs.anheyu.com/global/extra#:~:text=,internal_provider%3A%20cbd) [\[66\]](https://docs.anheyu.com/global/extra#:~:text=,https%3A%2F%2Fnpm.elemecdn.com%2F%24%7Bname%7D%40latest%2F%24%7Bfile) [\[96\]](https://docs.anheyu.com/global/extra#:~:text=%E5%9C%A8%20,Open%20Graph%20%E7%9A%84%E5%86%85%E5%AE%B9%EF%BC%8C%E5%B1%95%E7%A4%BA%E7%BC%A9%E7%95%A5%E5%9B%BE%EF%BC%8C%E6%A0%87%E9%A2%98%E7%AD%89%E7%AD%89%E4%BF%A1%E6%81%AF%E3%80%82) [\[97\]](https://docs.anheyu.com/global/extra#:~:text=,com%2Fdocs%2Fsharing%2Fwebmasters%2F%20Open_Graph_meta%3A%20enable%3A%20true) [\[98\]](https://docs.anheyu.com/global/extra#:~:text=enable%3A%20true%20option%3A%20,fb_app_id) [\[99\]](https://docs.anheyu.com/global/extra#:~:text=Inject) [\[100\]](https://docs.anheyu.com/global/extra#:~:text=inject%3A%20head%3A%20,%3Cscript%20src%3D%22xxxx%22%3E%3C%2Fscript) [\[101\]](https://docs.anheyu.com/global/extra#:~:text=yml) [\[102\]](https://docs.anheyu.com/global/extra#:~:text=3%204%205) [\[103\]](https://docs.anheyu.com/global/extra#:~:text=fancybox%20medium_zoom) [\[104\]](https://docs.anheyu.com/global/extra#:~:text=) [\[105\]](https://docs.anheyu.com/global/extra#:~:text=autoChangeMode%20%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2%E7%9A%84%E6%A8%A1%E5%BC%8F%20autoChangeMode%20autoChangeMode%3A%201,dark%20mode%20autoChangeMode%3A%20false%20%E5%8F%96%E6%B6%88%E8%87%AA%E5%8A%A8%E5%88%87%E6%8D%A2) [\[107\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L732%20%E4%B8%BB%E9%A2%98%E6%94%AF%E6%8C%81%20pace,js) [\[108\]](https://docs.anheyu.com/global/extra#:~:text=,io%2Fpace) [\[109\]](https://docs.anheyu.com/global/extra#:~:text=%E4%B8%BB%E9%A2%98%E6%94%AF%E6%8C%81%20pace) [\[110\]](https://docs.anheyu.com/global/extra#:~:text=%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E5%8A%A8%E7%94%BB) [\[111\]](https://docs.anheyu.com/global/extra#:~:text=match%20at%20L738%20,source) [\[112\]](https://docs.anheyu.com/global/extra#:~:text=%E7%99%BE%E5%BA%A6%E7%BB%9F%E8%AE%A1) [\[113\]](https://docs.anheyu.com/global/extra#:~:text=baidu_analytics%3A%20%E4%BD%A0%E7%9A%84%E4%BB%A3%E7%A0%81) [\[114\]](https://docs.anheyu.com/global/extra#:~:text=1) [\[115\]](https://docs.anheyu.com/global/extra#:~:text=google_analytics%3A%20%E4%BD%A0%E7%9A%84%E4%BB%A3%E7%A0%81%20%23%20%E9%80%9A%E5%B8%B8%E4%BB%A5%60UA) [\[116\]](https://docs.anheyu.com/global/extra#:~:text=Cloudflare) [\[117\]](https://docs.anheyu.com/global/extra#:~:text=%23%20Cloudflare%20Analytics%20%23%20https%3A%2F%2Fwww.cloudflare.com%2Fzh,cloudflare_analytics) [\[118\]](https://docs.anheyu.com/global/extra#:~:text=Microsoft%20Clarity) [\[119\]](https://docs.anheyu.com/global/extra#:~:text=4.%20%E4%BF%AE%E6%94%B9%20) [\[120\]](https://docs.anheyu.com/global/extra#:~:text=%E8%B0%B7%E6%AD%8C%E5%B9%BF%E5%91%8A) [\[121\]](https://docs.anheyu.com/global/extra#:~:text=google_adsense%3A%20enable%3A%20true%20auto_ads%3A%20true,%E5%A1%AB%E5%85%A5%E4%B8%AA%E4%BA%BA%E8%AF%86%E5%88%AB%E7%A0%81%20enable_page_level_ads%3A%20true) [\[122\]](https://docs.anheyu.com/global/extra#:~:text=CSS%20%E5%89%8D%E7%BC%80) [\[123\]](https://docs.anheyu.com/global/extra#:~:text=,to%20ensure%20compatibility%20css_prefix%3A%20true) [\[124\]](https://docs.anheyu.com/global/extra#:~:text=,left%20bg_light%3A%20%27%2349b1f5%27%20%23light%20mode%E6%97%B6%E5%BC%B9%E7%AA%97%E8%83%8C%E6%99%AF) [\[125\]](https://docs.anheyu.com/global/extra#:~:text=bottom,dark%20mode%E6%97%B6%E5%BC%B9%E7%AA%97%E8%83%8C%E6%99%AF) [\[150\]](https://docs.anheyu.com/global/extra#:~:text=,medium_zoom) [\[152\]](https://docs.anheyu.com/global/extra#:~:text=%E7%9F%AD%E6%A0%87%E7%AD%BE%20Tag%20Plugins) [\[153\]](https://docs.anheyu.com/global/extra#:~:text=%E7%9F%AD%E6%A0%87%E7%AD%BE%E8%99%BD%E7%84%B6%E8%83%BD%E4%B8%BA%E4%B8%BB%E9%A2%98%E5%B8%A6%E6%9D%A5%E4%B8%80%E4%BA%9B%E9%A2%9D%E5%A4%96%E7%9A%84%E5%8A%9F%E8%83%BD%E5%92%8C%20UI%20%E6%96%B9%E9%9D%A2%E7%9A%84%E5%BC%BA%E5%8C%96%EF%BC%8C%E4%BD%86%E6%98%AF%EF%BC%8C%E7%9F%AD%E6%A0%87%E7%AD%BE%E4%B9%9F%E6%9C%89%E6%98%8E%E6%98%BE%E7%9A%84%E9%99%90%E5%88%B6%EF%BC%8C%E4%BD%BF%E7%94%A8%E6%97%B6%E8%AF%B7%E7%95%99%E6%84%8F%E3%80%82) [\[157\]](https://docs.anheyu.com/global/extra#:~:text=,meting_js) 额外配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/global/extra>

[\[51\]](https://docs.anheyu.com/faq#:~:text=wordcount%20is%20not%20a%20function) [\[52\]](https://docs.anheyu.com/faq#:~:text=bash) [\[53\]](https://docs.anheyu.com/faq#:~:text=%E4%BA%A7%E7%94%9F%E5%8E%9F%E5%9B%A0%EF%BC%9A%E5%BC%80%E5%90%AF%E4%BA%86wordcount%E7%9A%84%E5%AD%97%E6%95%B0%E7%BB%9F%E8%AE%A1%EF%BC%8C%E4%BD%86%E6%98%AF%E6%B2%A1%E6%9C%89%E5%AE%89%E8%A3%85%E5%AF%B9%E5%BA%94%E6%8F%92%E4%BB%B6%E3%80%82) [\[171\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%BA%E4%BB%80%E4%B9%88%E6%88%91%E7%9A%84%E5%8D%9A%E5%AE%A2%E6%98%BE%E7%A4%BA%E7%9A%84%E5%BE%88%E5%A4%A7%EF%BC%9F) [\[172\]](https://docs.anheyu.com/faq#:~:text=%E7%AD%94%EF%BC%9A%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20) [\[173\]](https://docs.anheyu.com/faq#:~:text=yaml) [\[177\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%BA%E4%BB%80%E4%B9%88%E6%88%91%E7%9A%84%E5%8D%9A%E5%AE%A2%E6%98%BE%E7%A4%BA%E7%9A%84%E5%BE%88%E5%A4%A7%EF%BC%9F) [\[178\]](https://docs.anheyu.com/faq#:~:text=%E4%B8%8D%E8%A6%81%E6%85%8C) 常见问题 | 安知鱼主题官方文档

<https://docs.anheyu.com/faq>

[\[68\]](https://docs.anheyu.com/page/classify#:~:text=1) [\[69\]](https://docs.anheyu.com/page/classify#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20) [\[70\]](https://docs.anheyu.com/page/classify#:~:text=4.%20%E4%BF%AE%E6%94%B9%E8%BF%99%E4%B8%AA%E6%96%87%E4%BB%B6%EF%BC%9A%20%E8%AE%B0%E5%BE%97%E6%B7%BB%E5%8A%A0%20%60type%3A%20) [\[71\]](https://docs.anheyu.com/page/classify#:~:text=,) 分类页配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/page/classify>

[\[72\]](https://docs.anheyu.com/page/tags#:~:text=1) [\[73\]](https://docs.anheyu.com/page/tags#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20) [\[74\]](https://docs.anheyu.com/page/tags#:~:text=,comments%3A%20false%20top_img%3A%20false) [\[75\]](https://docs.anheyu.com/page/tags#:~:text=%E5%8F%82%E6%95%B0%20%E8%A7%A3%E9%87%8A%20type%E3%80%96%E5%BF%85%E9%A1%BB%E3%80%97%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%EF%BC%8C%E5%BF%85%E9%A1%BB%E4%B8%BA%20tags%20comments%E3%80%96%E5%8F%AF%E9%80%89%E3%80%97%E6%98%AF%E5%90%A6%E6%98%BE%E7%A4%BA%E8%AF%84%E8%AE%BA,1%2C%20desc%20for%20descending) 标签页配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/page/tags>

[\[86\]](https://docs.anheyu.com/page/about#:~:text=1) [\[87\]](https://docs.anheyu.com/page/about#:~:text=3.%20%E4%BD%A0%E4%BC%9A%E6%89%BE%E5%88%B0%20) [\[88\]](https://docs.anheyu.com/page/about#:~:text=%E5%85%B3%E4%BA%8E%3A%20%E5%85%B3%E4%BA%8E%E6%9C%AC%E4%BA%BA%3A%20%2Fabout%2F%20%7C%7C%20icon,prints1) [\[89\]](https://docs.anheyu.com/page/about#:~:text=%E6%96%B0%E5%BB%BA%20) [\[90\]](https://docs.anheyu.com/page/about#:~:text=,aboutsiteTips%3A%20tips%3A%20%E8%BF%BD%E6%B1%82%20title1%3A%20%E6%BA%90%E4%BA%8E) [\[91\]](https://docs.anheyu.com/page/about#:~:text=,%E6%99%BA%E8%83%BD%E5%AE%B6%E5%B1%85%E5%B0%8F%E8%83%BD%E6%89%8B) 关于页面配置 | 安知鱼主题官方文档

<https://docs.anheyu.com/page/about>

[\[135\]](https://tunglamc.github.io/20240607123500/#:~:text=giscus%3A%20repo%3A%20,CN) hexo-theme-anzhiyu 主题配置 giscus 评论系统 | TungLam

<https://tunglamc.github.io/20240607123500/>

[\[140\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%E7%84%B6%E5%90%8E%E5%9C%A8%20hexo%20%E7%9A%84%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20,%E6%96%87%E6%A1%A3%EF%BC%9A) [\[141\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=model%3A%20use%3A%20live2d,300%20mobile%3A%20show%3A%20true%20react) [\[142\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=live2d%3A%20enable%3A%20true%20scriptFrom%3A%20local,tagMode%3A%20false%20debug%3A%20false%20model) [\[143\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=display%3A%20position%3A%20right%20width%3A%20150,7) [\[144\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%E7%84%B6%E5%90%8E%E4%B8%8B%E8%BD%BD%E6%A8%A1%E5%9E%8B%EF%BC%8C%E6%A8%A1%E5%9E%8B%E5%90%8D%E7%A7%B0%E5%8F%AF%E4%BB%A5%E5%88%B0%20%E8%BF%99%E9%87%8C%20%E5%8F%82%E8%80%83%EF%BC%8C%E4%B8%80%E4%BA%9B%E6%A8%A1%E5%9E%8B%E7%9A%84%E9%A2%84%E8%A7%88%E5%8F%AF%E4%BB%A5%E5%9C%A8%20%E8%BF%99%E9%87%8C%E3%80%82) [\[145\]](https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/#:~:text=%2A%20GitHub%EF%BC%9Ahttps%3A%2F%2Fgithub.com%2FEYHN%2Fhexo,2.0) Hexo 博客添加 Live2D 看板娘 | 小决的专栏

<https://jueee.github.io/2020/10/2020-10-09-Hexo%E5%8D%9A%E5%AE%A2%E6%B7%BB%E5%8A%A0Live2D%E7%9C%8B%E6%9D%BF%E5%A8%98/>

[\[146\]](https://blog.csdn.net/weixin_43868299/article/details/108949274#:~:text=live2d) 使用Hexo的helper-live2d插件自定义博客看板娘原创

<https://blog.csdn.net/weixin_43868299/article/details/108949274>

[\[155\]](https://netlify.serms.top/posts/b9cf97e0#:~:text=anzhiyu%20%E4%B8%BB%E9%A2%98%E4%BC%98%E5%8C%96,%E6%98%AF%E5%90%A6%E5%90%AF%E7%94%A8fontawesome6%E5%9B%BE%E6%A0%87) anzhiyu 主题优化| SerMs

<https://netlify.serms.top/posts/b9cf97e0>

[\[156\]](https://docs.anheyu.com/global/base#:~:text=%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95%EF%BC%8C%E5%B0%86%E5%9B%BE%E6%A0%87%E5%BA%93%E4%B8%AD%E7%9A%84%E5%9B%BE%E6%A0%87%E5%90%8D%E5%A4%8D%E5%88%B6%EF%BC%8C%E7%84%B6%E5%90%8E%E5%8A%A0%E4%B8%8A%E5%89%8D%E7%BC%80%20anzhiyu,) 基础配置 - 安知鱼主题官方文档

<https://docs.anheyu.com/global/base>

[\[158\]](https://blog.anheyu.com/posts/asdx.html#:~:text=,SiteToken%5D%E7%94%B3%E8%AF%B7%E5%88%B0%E7%9A%84%E4%BB%A4%E7%89%8C%E7%A0%81%20Site%20%E6%8C%87%E7%AB%99%E7%82%B9%EF%BC%8C%E6%95%99%E7%A8%8B%E4%B8%AD%E4%BC%9A%E6%9B%BF%E6%8D%A2%E6%88%90%20Github%E3%80%81Gitee%E3%80%81Coding) [\[159\]](https://blog.anheyu.com/posts/asdx.html#:~:text=%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%98%AF%20butterfly%20%E4%B8%BB%E9%A2%98%EF%BC%8C%E8%AE%B0%E5%BE%97%E6%9B%BF%E6%8D%A2%E6%9C%80%E5%90%8E%E4%B8%80%E8%A1%8C%E5%86%85%E5%AE%B9%E4%B8%BA%E4%BD%A0%E8%87%AA%E5%B7%B1%E5%BD%93%E5%89%8D%E4%BD%BF%E7%94%A8%E7%9A%84%E4%B8%BB%E9%A2%98%E3%80%82%202.%E6%8F%90%E4%BA%A4%E6%BA%90%E7%A0%81%E5%88%B0%E7%A7%81%E6%9C%89%E4%BB%93%E5%BA%93%60,git%20%E6%8C%87%E4%BB%A4%E9%87%8D%E8%AE%BE%E4%BB%93%E5%BA%93%E5%9C%B0%E5%9D%80%E3%80%82%E8%BF%99%E6%A0%B7%E5%9C%A8%E6%96%B0%E5%BB%BA%E4%BB%93%E5%BA%93%EF%BC%8C%E6%88%91%E4%BB%AC%E4%BB%8D%E6%97%A7%E5%8F%AF%E4%BB%A5%E4%BF%9D%E7%95%99%E7%8F%8D%E8%B4%B5%E7%9A%84%20commit%20history%EF%BC%8C%E4%BE%BF%E4%BA%8E%E7%89%88%E6%9C%AC%E5%9B%9E%E6%BB%9A%E3%80%82) [\[160\]](https://blog.anheyu.com/posts/asdx.html#:~:text=%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E5%88%9B%E5%BB%BA%E4%B8%80%E4%B8%AA%E7%94%A8%E6%9D%A5%E5%AD%98%E6%94%BE%20%60Hexo%60%20%E5%8D%9A%E5%AE%A2%E6%BA%90%E7%A0%81%E7%9A%84%E7%A7%81%E6%9C%89%E4%BB%93%E5%BA%93%60,Image%207) [\[161\]](https://blog.anheyu.com/posts/asdx.html#:~:text=match%20at%20L517%201,SourceRepo%5D%60%E4%BA%86%E3%80%82%E6%BA%90%E7%A0%81%E4%BB%93%E5%BA%93%E4%B8%8D%E9%9C%80%E8%A6%81%E6%9C%89%20%60node_modules%20%60%E6%96%87%E4%BB%B6%E5%A4%B9%E3%80%82) [\[163\]](https://blog.anheyu.com/posts/asdx.html#:~:text=1.%20%E6%98%AF%E5%90%A6%E5%B0%86%20%60node_modules%20%60%E4%B9%9F%E4%B8%8A%E4%BC%A0%E5%88%B0%E6%BA%90%E7%A0%81%E4%BB%93%E5%BA%93%60,%E6%96%87%E4%BB%B6%E5%A4%B9%E3%80%82) 使用 Github Action 自动部署 | 安知鱼

<https://blog.anheyu.com/posts/asdx.html>

[\[162\]](https://blog.csdn.net/qq_36667170/article/details/118163563#:~:text=%E6%88%91%E4%BB%AC%E8%A6%81%E7%9F%A5%E9%81%93%EF%BC%8C%E5%9C%A8%E6%88%91%E4%BB%AC%E7%9A%84hexo%E5%8D%9A%E5%AE%A2%E6%96%87%E4%BB%B6%E4%B8%AD%E6%9C%89%E4%B8%80%E4%B8%AA%20) 换电脑后怎么迁移hexo博客？ 原创

<https://blog.csdn.net/qq_36667170/article/details/118163563>

[\[164\]](https://cloud.tencent.com/developer/article/1835685#:~:text=) [\[165\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E5%A4%8D%E5%88%B6) [\[166\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%24%20ssh,%2F%2F%E5%BC%95%E5%8F%B7%E9%87%8C%E9%9D%A2%E5%A1%AB%E5%86%99%E4%BD%A0%E7%9A%84%E9%82%AE%E7%AE%B1%E5%9C%B0%E5%9D%80%EF%BC%8C%E6%AF%94%E5%A6%82%E6%88%91%E7%9A%84%E6%98%AF1907065810%40qq.com) [\[167\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E5%A6%82%E4%BD%95%E5%B0%86hexo%E8%BF%81%E5%88%B0%E4%B8%80%E5%8F%B0%E6%96%B0%E7%94%B5%E8%84%91%E4%B8%8A%20,%E8%A7%A3%E5%8E%8B%E4%BB%A5%E5%90%8E%E5%9C%A8) [\[168\]](https://cloud.tencent.com/developer/article/1835685#:~:text=%E7%BD%91%E4%B8%8A%E7%9A%84%E5%85%B6%E4%BB%96%E6%95%99%E7%A8%8B%E9%83%BD%E5%BE%88%E5%A4%8D%E6%9D%82%E7%B9%81%E7%90%90) 如何将hexo迁到一台新电脑上-腾讯云开发者社区-腾讯云

<https://cloud.tencent.com/developer/article/1835685>

[\[174\]](https://www.fomal.cc/posts/3451f874.html#:~:text=Hexo%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B%28%E4%B8%89%29%20,%EF%BC%9B%E5%A6%82%E6%9E%9C%E4%B8%8D%E6%83%B3%E5%9C%A8%E9%A6%96%E9%A1%B5%E6%98%BE%E7%A4%BAcover%EF%BC%8C%E5%8F%AF%E4%BB%A5%E8%AE%BE%E7%BD%AE%E4%B8%BA%20false%20%E3%80%82%20%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%20_config) Hexo博客搭建基础教程(三) - Fomalhaut

<https://www.fomal.cc/posts/3451f874.html>
