/* ========== 首页轮播图修复 ========== */

/* 修复轮播图容器 */
#home_top .swiper-container,
.blog-slider {
  position: relative !important;
  overflow: hidden !important;
}

/* 修复轮播图导航按钮位置 */
.blog-slider .swiper-button-prev,
.blog-slider .swiper-button-next {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--anzhiyu-white) !important;
  opacity: 0.7 !important;
  transition: opacity 0.3s ease !important;
}

.blog-slider .swiper-button-prev {
  left: 20px !important;
}

.blog-slider .swiper-button-next {
  right: 20px !important;
}

.blog-slider:hover .swiper-button-prev,
.blog-slider:hover .swiper-button-next {
  opacity: 1 !important;
}

/* 修复分页器位置 */
.blog-slider .swiper-pagination {
  position: absolute !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 10 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 5px !important;
}

.blog-slider .swiper-pagination-bullet {
  width: 10px !important;
  height: 10px !important;
  background: rgba(255, 255, 255, 0.5) !important;
  opacity: 1 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.blog-slider .swiper-pagination-bullet-active {
  width: 30px !important;
  border-radius: 5px !important;
  background: var(--anzhiyu-white) !important;
}

/* 修复首页顶部轮播图样式 */
#home_top {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
}

#home_top .blog-slider {
  width: 100%;
  height: 100%;
  position: relative;
  margin: 0;
}

#home_top .blog-slider__item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
}

#home_top .blog-slider__img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
  z-index: 0;
}

#home_top .blog-slider__img::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

#home_top .blog-slider__content {
  position: relative;
  z-index: 5;
  text-align: center;
  color: #fff;
  padding: 0 20px;
  max-width: 800px;
}

#home_top .blog-slider__title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

#home_top .blog-slider__text {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
}

#home_top .blog-slider__button {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 25px;
  background: var(--anzhiyu-main);
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#home_top .blog-slider__button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  background: var(--anzhiyu-main-light);
}

/* 修复分类图标位置 */
#home_top .category-lists {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  z-index: 10;
}

#home_top .category-lists .category-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#home_top .category-lists .category-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#home_top .category-lists .category-item i {
  font-size: 1.2rem;
}

#home_top .category-lists .category-item span {
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 768px) {
  #home_top .blog-slider__title {
    font-size: 2rem;
  }

  #home_top .blog-slider__text {
    font-size: 1rem;
  }

  .blog-slider .swiper-button-prev,
  .blog-slider .swiper-button-next {
    width: 40px !important;
    height: 40px !important;
    font-size: 20px !important;
  }

  .blog-slider .swiper-button-prev {
    left: 10px !important;
  }

  .blog-slider .swiper-button-next {
    right: 10px !important;
  }

  #home_top .category-lists {
    flex-direction: column;
    gap: 10px;
    bottom: 20px;
  }

  #home_top .category-lists .category-item {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

/* 修复轮播图过渡动画 */
.blog-slider .swiper-slide {
  opacity: 0 !important;
  transition: opacity 1s ease !important;
}

.blog-slider .swiper-slide-active {
  opacity: 1 !important;
}

.blog-slider .swiper-slide-prev,
.blog-slider .swiper-slide-next {
  opacity: 0.3 !important;
}

/* 确保图标字体正确加载 */
.blog-slider .swiper-button-prev::after,
.blog-slider .swiper-button-next::after {
  font-family: 'anzhiyufont' !important;
  font-size: 24px !important;
}

.blog-slider .swiper-button-prev::after {
  content: '\e90e' !important; /* 左箭头图标 */
}

.blog-slider .swiper-button-next::after {
  content: '\e90f' !important; /* 右箭头图标 */
}

/* 修复可能的z-index层级问题 */
#home_top {
  z-index: 1;
}

#home_top .swiper-container {
  z-index: 2;
}

#home_top .swiper-wrapper {
  z-index: 3;
}

#home_top .swiper-pagination,
#home_top .swiper-button-prev,
#home_top .swiper-button-next {
  z-index: 15;
}

/* 添加加载动画 */
#home_top .blog-slider {
  animation: fadeIn 0.8s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 确保主题颜色变量正常 */
:root {
  --swiper-theme-color: var(--anzhiyu-main);
  --swiper-navigation-size: 44px;
}

/* 深色模式适配 */
[data-theme="dark"] #home_top .category-lists .category-item {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] #home_top .category-lists .category-item:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* ========== 关于页面轮播修复 ========== */

/* 关于页面整体容器 */
#about-page {
  position: relative;
  width: 100%;
  overflow: visible;
}

/* 关于页面轮播容器修复 */
#about-page .author-content-item.personality.swiper-container,
#about-page .author-content-item.myphoto.swiper-container {
  position: relative !important;
  overflow: hidden !important;
  border-radius: 12px;
}

/* 隐藏关于页面所有轮播导航按钮 */
#about-page .swiper-button-prev,
#about-page .swiper-button-next,
.about .swiper-button-prev,
.about .swiper-button-next,
#about-page .author-content-item .swiper-button-prev,
#about-page .author-content-item .swiper-button-next,
.author-content-item.personality .swiper-button-prev,
.author-content-item.personality .swiper-button-next,
.author-content-item.myphoto .swiper-button-prev,
.author-content-item.myphoto .swiper-button-next,
.skills-style-group .swiper-button-prev,
.skills-style-group .swiper-button-next {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 关于页面分页器 */
#about-page .swiper-pagination {
  position: absolute !important;
  bottom: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 50 !important;
  display: flex !important;
  justify-content: center !important;
  gap: 5px !important;
}

#about-page .swiper-pagination-bullet {
  width: 8px !important;
  height: 8px !important;
  background: var(--anzhiyu-fontcolor) !important;
  opacity: 0.3 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
}

#about-page .swiper-pagination-bullet-active {
  width: 24px !important;
  border-radius: 4px !important;
  background: var(--anzhiyu-main) !important;
  opacity: 1 !important;
}

/* 个性特质卡片轮播 */
#about-page .author-content-item.personality .swiper-slide {
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#about-page .author-content-item.personality .personality-item {
  width: 100%;
  padding: 20px;
  text-align: center;
}

/* 照片轮播 */
#about-page .author-content-item.myphoto .swiper-slide {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 10px;
}

#about-page .author-content-item.myphoto img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 技能标签轮播特殊处理 */
#about-page .skills-style-group .swiper-container {
  position: relative !important;
  overflow: hidden !important;
}

#about-page .skills-style-group .swiper-button-prev,
#about-page .skills-style-group .swiper-button-next {
  width: 36px !important;
  height: 36px !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 禁用状态样式 */
#about-page .swiper-button-disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

#about-page .swiper-button-disabled:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  transform: translateY(-50%) !important;
}

/* 响应式适配 - 关于页面 */
@media (max-width: 768px) {
  #about-page .swiper-button-prev,
  #about-page .swiper-button-next {
    width: 32px !important;
    height: 32px !important;
  }
  
  #about-page .swiper-button-prev {
    left: 5px !important;
  }
  
  #about-page .swiper-button-next {
    right: 5px !important;
  }
  
  #about-page .swiper-button-prev::after,
  #about-page .swiper-button-next::after {
    font-size: 14px !important;
  }
}

/* 深色模式 - 关于页面轮播 */
[data-theme="dark"] #about-page .swiper-button-prev,
[data-theme="dark"] #about-page .swiper-button-next {
  background: rgba(30, 30, 30, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] #about-page .swiper-button-prev:hover,
[data-theme="dark"] #about-page .swiper-button-next:hover {
  background: var(--anzhiyu-main) !important;
}

[data-theme="dark"] #about-page .swiper-button-prev::after,
[data-theme="dark"] #about-page .swiper-button-next::after {
  color: rgba(255, 255, 255, 0.9) !important;
}

[data-theme="dark"] #about-page .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* 修复关于页面轮播容器层级 */
#about-page .author-content-item {
  position: relative;
  z-index: 1;
}

#about-page .author-content-item .swiper-wrapper {
  z-index: 2;
}

#about-page .author-content-item .swiper-button-prev,
#about-page .author-content-item .swiper-button-next,
#about-page .author-content-item .swiper-pagination {
  z-index: 10;
}

/* 确保轮播切换平滑 */
#about-page .swiper-slide {
  transition-property: opacity, transform !important;
}

#about-page .swiper-container-horizontal > .swiper-wrapper {
  transition-timing-function: ease-out !important;
}

/* ========== 修复技能卡片装饰元素溢出 ========== */

/* 只对关于页面的技能卡片进行限制，不影响其他页面 */
#about-page .author-content-item.skills {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  flex: 1.5;
  min-width: 60%;
}

#about-page .author-content-item.skills .card-content {
  position: relative;
  z-index: 2;
}

#about-page .skills-style-group {
  position: relative;
  min-height: 300px;
  max-height: 400px;
}

/* ========== 调整技能和生涯卡片布局 ========== */

/* 调整包含技能和生涯的容器为横向布局 */
#about-page .author-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

/* 技能和生涯卡片的父容器 */
#about-page .author-content:has(.skills):has(.careers) {
  display: flex !important;
  flex-direction: row !important;
  gap: 20px !important;
  align-items: stretch !important;
}

/* 生涯卡片调整 */
#about-page .author-content-item.careers {
  flex: 1 !important;
  min-width: 35% !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
}

#about-page .author-content-item.careers .card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 生涯列表样式优化 */
#about-page .careers-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px 0;
}

#about-page .careers-group .career-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 10px 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
}

#about-page .careers-group .career-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(5px);
}

#about-page .careers-group .circle {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50%;
  flex-shrink: 0;
}

#about-page .careers-group .name {
  font-size: 14px;
  font-weight: 500;
  color: var(--anzhiyu-fontcolor);
}

/* 生涯卡片图片 */
#about-page .author-content-item.careers .author-content-img {
  margin-top: auto;
  max-height: 150px;
  object-fit: contain;
}

/* 响应式布局 - 移动端 */
@media (max-width: 768px) {
  #about-page .author-content:has(.skills):has(.careers) {
    flex-direction: column !important;
  }
  
  #about-page .author-content-item.skills,
  #about-page .author-content-item.careers {
    min-width: 100% !important;
    width: 100% !important;
  }
}

/* 统一卡片高度 */
#about-page .author-content-item.skills,
#about-page .author-content-item.careers {
  min-height: 350px;
  max-height: 400px;
}

/* 深色模式适配 */
[data-theme="dark"] #about-page .careers-group .career-item {
  background: rgba(30, 30, 30, 0.5);
}

[data-theme="dark"] #about-page .careers-group .career-item:hover {
  background: rgba(30, 30, 30, 0.8);
}

/* ========== 主页布局间距优化 ========== */
/* 参考 blog.csun.site 的紧凑布局设计 */

/* 首先调整顶部区域高度，让其更紧凑 */
#home_top {
  height: 50vh !important;
  min-height: 400px !important;
  margin-bottom: 0 !important;
}

/* 调整站点信息位置，让其更居中 */
#home_top .site_info {
  top: 40% !important;
  transform: translateY(-50%) !important;
}

/* 调整分类卡片位置，让其更靠近底部 */
#home_top .category-lists {
  bottom: 30px !important;
}

/* 主要调整：将文章列表向上移动，实现紧凑布局 */
#recent-posts {
  margin-top: -100px !important;
  position: relative;
  z-index: 2;
  padding-top: 50px !important;
}

/* 确保文章卡片有足够的背景和阴影 */
#recent-posts .recent-post-item {
  background: var(--anzhiyu-card-bg) !important;
  box-shadow: var(--anzhiyu-shadow-border) !important;
  border-radius: 12px !important;
  position: relative;
  z-index: 3;
}

/* 调整主布局容器 */
.layout {
  margin-top: 0 !important;
  position: relative;
  z-index: 2;
}

/* 调整主内容区域 */
#main {
  margin-top: 0 !important;
  position: relative;
  z-index: 2;
}

/* 响应式调整 */
@media (max-width: 768px) {
  #home_top {
    height: 60vh !important;
    min-height: 350px !important;
  }

  #home_top .site_info {
    top: 35% !important;
  }

  #recent-posts {
    margin-top: -80px !important;
    padding-top: 40px !important;
  }
}

@media (max-width: 1200px) {
  #home_top {
    height: 55vh !important;
    min-height: 380px !important;
  }

  #recent-posts {
    margin-top: -90px !important;
    padding-top: 45px !important;
  }
}
