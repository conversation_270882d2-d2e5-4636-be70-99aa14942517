# Waline 评论系统故障诊断工具
# PowerShell 版本

Write-Host "🔍 Waline 评论系统故障诊断工具" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js版本
Write-Host "📋 1. 检查Node.js环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 检查端口8360占用情况
Write-Host ""
Write-Host "📋 2. 检查端口8360占用情况..." -ForegroundColor Yellow
$port8360 = Get-NetTCPConnection -LocalPort 8360 -ErrorAction SilentlyContinue
if ($port8360) {
    Write-Host "✅ 端口8360正在被使用" -ForegroundColor Green
    $processId = $port8360.OwningProcess
    $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
    if ($process) {
        Write-Host "   进程: $($process.ProcessName) (PID: $processId)" -ForegroundColor Cyan
    }
} else {
    Write-Host "⚠️  端口8360未被占用" -ForegroundColor Yellow
}

# 检查Waline本地服务器文件
Write-Host ""
Write-Host "📋 3. 检查Waline服务器文件..." -ForegroundColor Yellow
$walineLocalPath = ".\waline-local"
if (Test-Path $walineLocalPath) {
    Write-Host "✅ waline-local目录存在" -ForegroundColor Green
    
    # 检查关键文件
    $files = @("package.json", "index.js")
    foreach ($file in $files) {
        $filePath = Join-Path $walineLocalPath $file
        if (Test-Path $filePath) {
            Write-Host "✅ $file 存在" -ForegroundColor Green
        } else {
            Write-Host "❌ $file 不存在" -ForegroundColor Red
        }
    }
    
    # 检查node_modules
    $nodeModulesPath = Join-Path $walineLocalPath "node_modules"
    if (Test-Path $nodeModulesPath) {
        Write-Host "✅ node_modules 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ node_modules 不存在，需要运行 npm install" -ForegroundColor Red
    }
} else {
    Write-Host "❌ waline-local目录不存在" -ForegroundColor Red
}

# 测试服务器连接
Write-Host ""
Write-Host "📋 4. 测试Waline服务器连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8360" -Method GET -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Waline服务器响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法连接到Waline服务器" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查博客配置
Write-Host ""
Write-Host "📋 5. 检查博客配置..." -ForegroundColor Yellow
$configPath = ".\_config.anzhiyu.yml"
if (Test-Path $configPath) {
    Write-Host "✅ _config.anzhiyu.yml 存在" -ForegroundColor Green
    
    # 检查Waline配置
    $configContent = Get-Content $configPath -Raw
    if ($configContent -match "serverURL.*localhost:8360") {
        Write-Host "✅ Waline serverURL 配置正确" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Waline serverURL 可能配置不正确" -ForegroundColor Yellow
    }
    
    if ($configContent -match "use: Waline") {
        Write-Host "✅ 评论系统设置为Waline" -ForegroundColor Green
    } else {
        Write-Host "⚠️  评论系统可能未设置为Waline" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ _config.anzhiyu.yml 不存在" -ForegroundColor Red
}

# 检查数据库文件
Write-Host ""
Write-Host "📋 6. 检查SQLite数据库..." -ForegroundColor Yellow
$dbPath = ".\waline-local\data\waline.db"
if (Test-Path $dbPath) {
    $dbInfo = Get-Item $dbPath
    Write-Host "✅ SQLite数据库存在" -ForegroundColor Green
    Write-Host "   文件大小: $([math]::Round($dbInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
    Write-Host "   修改时间: $($dbInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "⚠️  SQLite数据库不存在（首次运行时会自动创建）" -ForegroundColor Yellow
}

# 提供解决方案
Write-Host ""
Write-Host "🔧 故障排除建议:" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

if (-not $port8360) {
    Write-Host "1. 启动Waline服务器:" -ForegroundColor White
    Write-Host "   cd waline-local && npm start" -ForegroundColor Gray
}

if (-not (Test-Path ".\waline-local\node_modules")) {
    Write-Host "2. 安装依赖:" -ForegroundColor White
    Write-Host "   cd waline-local && npm install" -ForegroundColor Gray
}

Write-Host ""
Write-Host "3. 如果仍有问题，尝试重置数据库:" -ForegroundColor White
Write-Host "   cd waline-local && npm run reset && npm start" -ForegroundColor Gray

Write-Host ""
Write-Host "4. 查看详细错误日志:" -ForegroundColor White
Write-Host "   检查PowerShell窗口中的错误信息" -ForegroundColor Gray

Write-Host ""
Write-Host "📞 需要进一步帮助？" -ForegroundColor Cyan
Write-Host "   1. 检查浏览器开发者工具的Console和Network选项卡" -ForegroundColor White
Write-Host "   2. 确保防火墙没有阻止端口8360" -ForegroundColor White
Write-Host "   3. 尝试使用test-waline.html测试页面" -ForegroundColor White

Write-Host ""
Write-Host "✨ 诊断完成！" -ForegroundColor Green
