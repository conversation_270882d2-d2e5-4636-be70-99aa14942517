const http = require('http');
const Waline = require('@waline/vercel');

// 创建Waline实例 - 使用内存存储
const walineHandler = Waline({
  // 强制使用内存存储
  STORAGE: 'memory',
  // 允许匿名评论
  ANONYMOUS: true,
  // 网站信息
  SITE_NAME: '郁离的博客',
  SITE_URL: 'http://localhost:4001',
  // 管理员邮箱
  AUTHOR_EMAIL: '<EMAIL>',
  // 禁用各种外部服务
  DISABLE_EMAIL_NOTIFY: true,
  DISABLE_REGION: true,
  COMMENT_AUDIT: false,
  // 日志级别
  LOG_LEVEL: 'info',
  // 设置为测试模式
  NODE_ENV: 'development'
});

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  // 设置CORS头，允许跨域访问
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // 使用Waline处理请求
    await walineHandler(req, res);
  } catch (error) {
    console.error('Waline error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Internal server error' }));
  }
});

const PORT = 8360;
server.listen(PORT, () => {
  console.log(`🚀 Waline server is running at http://localhost:${PORT}`);
  console.log(`📝 You can now use this URL in your blog config:`);
  console.log(`   serverURL: http://localhost:${PORT}`);
});
